import 'package:goldencyz_app/config/api_config.dart';
import 'package:goldencyz_app/models/gold_rate.dart';
import 'package:goldencyz_app/models/gold_rate_history.dart';
import 'package:goldencyz_app/services/api_service.dart';
import 'package:goldencyz_app/services/gold_rate_cache_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:logger/logger.dart';

class GoldRateService {
  final ApiService _apiService = ApiService();
  // Make cacheService public so it can be accessed from outside
  final GoldRateCacheService cacheService = GoldRateCacheService();
  final Logger _logger = Logger();
  bool _isInitialized = false;

  // Initialize the service
  Future<void> init() async {
    if (!_isInitialized) {
      await cacheService.init();
      _isInitialized = true;
    }
  }

  // Get latest gold rate
  Future<GoldRate> getLatestRate() async {
    await init();
    try {
      final response = await _apiService.get(ApiConfig.latestRate);
      _logger.i('Gold rate response: $response');

      if (response != null && response.containsKey('gold')) {
        // New format with both gold and silver rates
        final goldData = response['gold'];
        return GoldRate(
          date: DateTime.parse(goldData['date']),
          pricePerGram: double.parse(goldData['price_per_gram']),
          source: goldData['source'],
          karat: goldData['karat'] ?? '22K'
        );
      } else if (response != null && response.containsKey('rate')) {
        // Legacy format with only gold rate
        return GoldRate.fromJson(response['rate']);
      } else {
        _logger.w('Invalid response format from gold rate API: $response');
        // Return a default rate if the API fails
        return GoldRate(
          date: DateTime.now(),
          pricePerGram: 15000.0, // Default price
          source: 'Default',
          karat: '22K'
        );
      }
    } catch (e) {
      _logger.e('Error fetching latest gold rate: $e');
      // Return a default rate if the API fails
      return GoldRate(
        date: DateTime.now(),
        pricePerGram: 15000.0, // Default price
        source: 'Default',
        karat: '22K'
      );
    }
  }

  // Get gold rate history with caching
  Future<List<GoldRateHistory>> getRateHistory({
    int days = 30,
    bool forceRefresh = false,
  }) async {
    try {
      // Check cache first if not forcing refresh
      if (!forceRefresh) {
        final cachedData = await cacheService.getCachedRates(days);
        if (cachedData != null) {
          _logger.i('Using cached gold rate history data');
          return cachedData;
        }
      }

      // Make API request
      final response = await _apiService.get(
        '${ApiConfig.metalRates}/gold/history',
        queryParameters: {'days': days},
      );

      if (response == null) {
        throw Exception('Failed to get gold rate history');
      }

      final List<GoldRateHistory> rates = [];

      // Convert JSON data to GoldRateHistory objects
      for (var item in response['data']) {
        rates.add(GoldRateHistory.fromJson(item));
      }

      // Cache the rates
      await cacheService.cacheRates(rates, days);

      return rates;
    } catch (e) {
      _logger.e('Error getting gold rate history: $e');
      rethrow;
    }
  }

  // Get gold rate history for a specific date range
  Future<List<GoldRateHistory>> getRateHistoryByDateRange({
    required DateTime startDate,
    required DateTime endDate,
    bool forceRefresh = false
  }) async {
    await init();

    // Calculate days between dates
    final days = endDate.difference(startDate).inDays + 1;

    // Use the API with date range parameters
    try {
      final response = await _apiService.get(
        ApiConfig.rateHistory,
        queryParameters: {
          'start_date': startDate.toIso8601String().split('T')[0],
          'end_date': endDate.toIso8601String().split('T')[0],
          'gold_type': 'k22'
        },
      );

      List<dynamic> historyData;
      if (response.containsKey('gold') && response['gold'].containsKey('history')) {
        // New format with both gold and silver rates
        historyData = response['gold']['history'];
      } else if (response.containsKey('history')) {
        // Legacy format with only gold rate history
        historyData = response['history'];
      } else {
        throw Exception('Invalid response format from gold rate history API');
      }

      final List<GoldRateHistory> rates = [];

      // Convert JSON data to GoldRateHistory objects
      for (var item in historyData) {
        rates.add(GoldRateHistory.fromJson(item));
      }

      return rates;
    } catch (e) {
      _logger.e('Error fetching gold rate history by date range: $e');
      rethrow;
    }
  }

  // Background sync method
  Future<void> syncHistoricalData() async {
    await init();

    try {
      // Check connectivity first
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        _logger.w('Cannot sync historical data: Device is offline');
        return;
      }

      _logger.i('Starting background sync of historical gold rate data');

      // Sync last 30 days (high priority)
      await getRateHistory(days: 30, forceRefresh: true);

      // Sync last 90 days (medium priority)
      await getRateHistory(days: 90, forceRefresh: true);

      // Sync last 180 days (low priority)
      await getRateHistory(days: 180, forceRefresh: true);

      _logger.i('Background sync completed successfully');
    } catch (e) {
      _logger.e('Error during background sync: $e');
    }
  }
}
