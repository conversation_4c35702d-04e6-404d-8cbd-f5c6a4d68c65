import 'package:goldencyz_app/models/product.dart';

class DemoProductService {
  // Get all products
  Future<List<Product>> getProducts({String? type, int? categoryId, bool? featured}) async {
    print('DemoProductService.getProducts called with type=$type, categoryId=$categoryId, featured=$featured');
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    // Create demo products
    final List<Product> allProducts = _createDemoProducts();
    print('Created ${allProducts.length} demo products');

    // Filter products based on parameters
    List<Product> filteredProducts = allProducts;

    if (type != null) {
      filteredProducts = filteredProducts.where((p) => p.type == type).toList();
      print('Filtered by type=$type: ${filteredProducts.length} products');
    }

    if (categoryId != null) {
      filteredProducts = filteredProducts.where((p) => p.categoryId == categoryId).toList();
      print('Filtered by categoryId=$categoryId: ${filteredProducts.length} products');
    }

    if (featured != null) {
      filteredProducts = filteredProducts.where((p) => p.isFeatured == featured).toList();
      print('Filtered by featured=$featured: ${filteredProducts.length} products');
    }

    print('Returning ${filteredProducts.length} products from DemoProductService');
    return filteredProducts;
  }

  // Get product categories
  Future<List<ProductCategory>> getCategories() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    return [
      ProductCategory(
        id: 1,
        name: 'Gold',
        slug: 'gold',
        description: 'Gold products',
        icon: 'gold-icon',
        isActive: true,
        displayOrder: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      ProductCategory(
        id: 2,
        name: 'Silver',
        slug: 'silver',
        description: 'Silver products',
        icon: 'silver-icon',
        isActive: true,
        displayOrder: 2,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  // Get a specific product
  Future<Product> getProduct(int id) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    final products = _createDemoProducts();
    final product = products.firstWhere(
      (p) => p.id == id,
      orElse: () => throw Exception('Product not found'),
    );

    return product;
  }

  // Search products
  Future<List<Product>> searchProducts(String query) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    final products = _createDemoProducts();
    final lowercaseQuery = query.toLowerCase();

    return products.where((p) =>
      p.name.toLowerCase().contains(lowercaseQuery) ||
      (p.description?.toLowerCase().contains(lowercaseQuery) ?? false) ||
      p.type.toLowerCase().contains(lowercaseQuery)
    ).toList();
  }

  // Get featured products
  Future<List<Product>> getFeaturedProducts() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    final products = _createDemoProducts();
    return products.where((p) => p.isFeatured).toList();
  }

  // Get gold products
  Future<List<Product>> getGoldProducts() async {
    return getProducts(type: 'gold');
  }

  // Get silver products
  Future<List<Product>> getSilverProducts() async {
    return getProducts(type: 'silver');
  }

  // Create demo products
  List<Product> _createDemoProducts() {
    final now = DateTime.now();
    final goldCategory = ProductCategory(
      id: 1,
      name: 'Gold',
      slug: 'gold',
      description: 'Gold products',
      icon: 'gold-icon',
      isActive: true,
      displayOrder: 1,
      createdAt: now,
      updatedAt: now,
    );

    final silverCategory = ProductCategory(
      id: 2,
      name: 'Silver',
      slug: 'silver',
      description: 'Silver products',
      icon: 'silver-icon',
      isActive: true,
      displayOrder: 2,
      createdAt: now,
      updatedAt: now,
    );

    return [
      Product(
        id: 1,
        categoryId: 1,
        name: '1g Gold Bar',
        slug: '1g-gold-bar',
        description: 'Pure 22K gold bar weighing 1 gram',
        type: 'gold',
        weight: 1.0,
        weightInVori: 0.0857,
        purity: '91.67%',
        karat: '22K',
        basePrice: null,
        isPriceDynamic: true,
        priceSource: 'BAJUS',
        sku: 'GOLD-1G',
        image: 'products/gold-1g.jpg',
        stockQuantity: 50,
        lowStockThreshold: 10,
        isActive: true,
        isFeatured: true,
        privateProducts: false,
        displayOrder: 1,
        createdAt: now,
        updatedAt: now,
        category: goldCategory,
        currentPrice: 8500.0,
      ),
      Product(
        id: 2,
        categoryId: 1,
        name: '10g Gold Bar',
        slug: '10g-gold-bar',
        description: 'Pure 22K gold bar weighing 10 grams',
        type: 'gold',
        weight: 10.0,
        weightInVori: 0.857,
        purity: '99.99%',
        karat: '24K',
        basePrice: null,
        isPriceDynamic: true,
        priceSource: 'BAJUS',
        sku: 'GOLD-10G',
        image: 'products/gold-10g.jpg',
        stockQuantity: 20,
        lowStockThreshold: 5,
        isActive: true,
        isFeatured: true,
        privateProducts: true,
        displayOrder: 2,
        createdAt: now,
        updatedAt: now,
        category: goldCategory,
        currentPrice: 85000.0,
      ),
      Product(
        id: 3,
        categoryId: 2,
        name: '500g Silver Bar',
        slug: '500g-silver-bar',
        description: 'Pure silver bar weighing 500 grams',
        type: 'silver',
        weight: 500.0,
        weightInVori: 42.87,
        purity: '92.5%',
        karat: 'Sterling',
        basePrice: null,
        isPriceDynamic: true,
        priceSource: 'CUSTOM',
        sku: 'SILVER-500G',
        image: 'products/silver-500g.jpg',
        stockQuantity: 30,
        lowStockThreshold: 8,
        isActive: true,
        isFeatured: true,
        privateProducts: false,
        displayOrder: 3,
        createdAt: now,
        updatedAt: now,
        category: silverCategory,
        currentPrice: 60000.0,
      ),
      Product(
        id: 4,
        categoryId: 2,
        name: '1kg Silver Bar',
        slug: '1kg-silver-bar',
        description: 'Pure silver bar weighing 1 kilogram',
        type: 'silver',
        weight: 1000.0,
        weightInVori: 85.74,
        purity: '99.9%',
        karat: 'Fine',
        basePrice: null,
        isPriceDynamic: true,
        priceSource: 'SYSTEM',
        sku: 'SILVER-1KG',
        image: 'products/silver-1kg.jpg',
        stockQuantity: 15,
        lowStockThreshold: 3,
        isActive: true,
        isFeatured: false,
        privateProducts: true,
        displayOrder: 4,
        createdAt: now,
        updatedAt: now,
        category: silverCategory,
        currentPrice: 120000.0,
      ),
    ];
  }
}
