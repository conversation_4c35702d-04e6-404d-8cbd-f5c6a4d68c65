import 'package:goldencyz_app/config/api_config.dart';
import 'package:goldencyz_app/models/silver_rate.dart';
import 'package:goldencyz_app/services/api_service.dart';

class SilverRateService {
  final ApiService _apiService;

  SilverRateService(this._apiService);

  Future<SilverRate> getLatestRate() async {
    try {
      // Use the combined endpoint that returns both gold and silver rates
      final response = await _apiService.get(ApiConfig.latestRate);

      print('Silver rate response: $response');

      if (response != null && response.containsKey('silver')) {
        // New format with both gold and silver rates
        final silverData = response['silver'];
        print('DEBUG: Silver API response: $silverData');
        return SilverRate(
          date: silverData['date'],
          pricePerGram: double.parse(silverData['price_per_gram']),
          source: silverData['source'],
          karat: silverData['karat'] ?? 'Fine'
        );
      } else if (response != null && response.containsKey('rate')) {
        // Legacy format with only silver rate
        return SilverRate.fromJson(response['rate']);
      } else if (response != null && response.containsKey('success') && response['success'] == true && response.containsKey('rate')) {
        // Another legacy format
        return SilverRate.fromJson(response['rate']);
      } else {
        // Return a default rate if the API fails
        print('Warning: Using default silver rate due to invalid response format');
        return SilverRate(
          date: DateTime.now().toString().split(' ')[0], // Format as YYYY-MM-DD
          pricePerGram: 120.0, // Default price
          source: 'Default',
          karat: 'Fine'
        );
      }
    } catch (e) {
      throw Exception('Failed to load silver rate: $e');
    }
  }

  Future<SilverRateHistory> getRateHistory({int days = 30}) async {
    try {
      final response = await _apiService.get(
        '${ApiConfig.metalRates}/silver/history',
        queryParameters: {'days': days},
      );

      if (response == null) {
        throw Exception('Failed to get silver rate history');
      }

      return SilverRateHistory.fromJson(response['data']);
    } catch (e) {
      print('Error getting silver rate history: $e');
      rethrow;
    }
  }
}
