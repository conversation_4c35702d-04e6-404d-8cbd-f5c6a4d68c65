import 'package:goldencyz_app/config/api_config.dart';
import 'package:goldencyz_app/services/api_service.dart';
import 'package:logger/logger.dart';

class SmsService {
  final ApiService _apiService;
  final Logger _logger = Logger();

  SmsService(this._apiService);

  /// Send SMS to a phone number
  Future<Map<String, dynamic>> sendSms(String phoneNumber, String message) async {
    try {
      _logger.i('Sending SMS to $phoneNumber: $message');
      
      final response = await _apiService.post(
        ApiConfig.sendSms,
        data: {
          'phone_number': phoneNumber,
          'message': message,
        },
      );
      
      _logger.i('SMS sending response: $response');
      return response;
    } catch (e) {
      _logger.e('Error sending SMS: $e');
      return {
        'success': false,
        'message': 'Error sending SMS: $e',
      };
    }
  }

  /// Send OTP to a phone number
  Future<Map<String, dynamic>> sendOtp(String phoneNumber, String otp) async {
    final message = "GoldenCYZ  OTP: $otp. This code will expire in 10 minutes.";
    return sendSms(phoneNumber, message);
  }
}
