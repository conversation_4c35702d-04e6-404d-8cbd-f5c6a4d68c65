import 'package:goldencyz_app/config/api_config.dart';
import 'package:goldencyz_app/models/product.dart';
import 'package:goldencyz_app/models/gold_purchase.dart';
import 'package:goldencyz_app/models/silver_purchase.dart';
import 'package:goldencyz_app/services/api_service.dart';

class ProductService {
  final ApiService _apiService;

  ProductService(this._apiService);

  // Get all products
  Future<List<Product>> getProducts({String? type, int? categoryId, bool? featured}) async {
    try {
      Map<String, dynamic> queryParams = {};

      if (type != null) {
        queryParams['type'] = type;
      }

      if (categoryId != null) {
        queryParams['category_id'] = categoryId.toString();
      }

      if (featured != null) {
        queryParams['featured'] = featured ? '1' : '0';
      }

      print('Fetching products from ${ApiConfig.products} with params $queryParams');
      final response = await _apiService.get(
        ApiConfig.products, // Remove baseUrl, it's already added in ApiService
        queryParameters: queryParams,
      );
      print('Products API response: $response');

      // If we don't have any products yet, return demo products
      if (response == null ||
          (!response.containsKey('products') &&
           !response.containsKey('data'))) {
        print('No products found in API response, using demo products');
        return _createDemoProducts();
      }

      if (response.containsKey('success') && response['success'] == true) {
        final List<dynamic> productsJson = response['products'] ?? response['data'] ?? [];
        print('Found ${productsJson.length} products in response');

        if (productsJson.isEmpty) {
          print('Empty products list in API response, using demo products');
          return _createDemoProducts();
        }

        final products = productsJson.map((json) => Product.fromJson(json)).toList();
        print('Parsed ${products.length} products successfully');
        return products;
      } else {
        print('API returned error: ${response['message']}');
        print('Using demo products as fallback');
        return _createDemoProducts();
      }
    } catch (e) {
      print('Error loading products: $e');
      print('Using demo products as fallback');
      return _createDemoProducts();
    }
  }

  // Get product categories
  Future<List<ProductCategory>> getCategories() async {
    try {
      final response = await _apiService.get(
        ApiConfig.productCategories,
      );

      if (response.containsKey('success') && response['success'] == true) {
        final List<dynamic> categoriesJson = response['categories'] ?? response['data'] ?? [];
        return categoriesJson.map((json) => ProductCategory.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load categories: ${response['message']}');
      }
    } catch (e) {
      throw Exception('Failed to load categories: $e');
    }
  }

  // Get a specific product
  Future<Product> getProduct(int id) async {
    try {
      final response = await _apiService.get(
        ApiConfig.products + '/$id',
      );

      if (response.containsKey('success') && response['success'] == true) {
        return Product.fromJson(response['product'] ?? response['data']);
      } else {
        throw Exception('Failed to load product: ${response['message']}');
      }
    } catch (e) {
      throw Exception('Failed to load product: $e');
    }
  }

  // Search products
  Future<List<Product>> searchProducts(String query) async {
    try {
      final response = await _apiService.get(
        ApiConfig.searchProducts,
        queryParameters: {'query': query},
      );

      if (response.containsKey('success') && response['success'] == true) {
        final List<dynamic> productsJson = response['products'];
        return productsJson.map((json) => Product.fromJson(json)).toList();
      } else {
        throw Exception('Failed to search products: ${response['message']}');
      }
    } catch (e) {
      throw Exception('Failed to search products: $e');
    }
  }

  // Get featured products
  Future<List<Product>> getFeaturedProducts() async {
    try {
      final response = await _apiService.get(
        ApiConfig.featuredProducts,
      );

      if (response.containsKey('success') && response['success'] == true) {
        final List<dynamic> productsJson = response['products'] ?? response['data'] ?? [];
        return productsJson.map((json) => Product.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load featured products: ${response['message']}');
      }
    } catch (e) {
      throw Exception('Failed to load featured products: $e');
    }
  }

  // Get gold products
  Future<List<Product>> getGoldProducts() async {
    return getProducts(type: 'gold');
  }

  // Get silver products
  Future<List<Product>> getSilverProducts() async {
    return getProducts(type: 'silver');
  }

  // Get products by category
  Future<List<Product>> getProductsByCategory(String categorySlug, {bool? featured}) async {
    try {
      Map<String, dynamic> queryParams = {};

      if (featured != null) {
        queryParams['featured'] = featured ? '1' : '0';
      }

      final response = await _apiService.get(
        ApiConfig.productsByCategory + '/$categorySlug',
        queryParameters: queryParams,
      );

      if (response.containsKey('success') && response['success'] == true) {
        final List<dynamic> productsJson = response['products'];
        return productsJson.map((json) => Product.fromJson(json)).toList();
      } else {
        print('API returned error: ${response['message']}');
        print('Using demo products as fallback');
        return _createDemoProducts();
      }
    } catch (e) {
      print('Error loading products by category: $e');
      print('Using demo products as fallback');
      return _createDemoProducts();
    }
  }

  // Create demo products
  List<Product> _createDemoProducts() {
    final now = DateTime.now();

    return [
      Product(
        id: 1,
        categoryId: 1,
        name: '1g Gold Bar',
        slug: '1g-gold-bar',
        description: 'Pure 22K gold bar weighing 1 gram',
        type: 'gold',
        weight: 1.0,
        weightInVori: 0.0857,
        purity: '91.67%',
        karat: '22K',
        basePrice: 15251.0,
        isPriceDynamic: true,
        priceSource: 'BAJUS',
        sku: 'GOLD-1G',
        image: 'products/gold-1g.jpg',
        stockQuantity: 50,
        lowStockThreshold: 10,
        isActive: true,
        isFeatured: true,
        privateProducts: false,
        displayOrder: 1,
        createdAt: now,
        updatedAt: now,
        currentPrice: 15251.0,
      ),
      Product(
        id: 2,
        categoryId: 1,
        name: '10g Gold Bar',
        slug: '10g-gold-bar',
        description: 'Pure 22K gold bar weighing 10 grams',
        type: 'gold',
        weight: 10.0,
        weightInVori: 0.857,
        purity: '99.99%',
        karat: '24K',
        basePrice: 152510.0,
        isPriceDynamic: true,
        priceSource: 'BAJUS',
        sku: 'GOLD-10G',
        image: 'products/gold-10g.jpg',
        stockQuantity: 20,
        lowStockThreshold: 5,
        isActive: true,
        isFeatured: true,
        privateProducts: true,
        displayOrder: 2,
        createdAt: now,
        updatedAt: now,
        currentPrice: 152510.0,
      ),
      Product(
        id: 3,
        categoryId: 2,
        name: '500g Silver Bar',
        slug: '500g-silver-bar',
        description: 'Pure silver bar weighing 500 grams',
        type: 'silver',
        weight: 500.0,
        weightInVori: 42.87,
        purity: '92.5%',
        karat: 'Sterling',
        basePrice: 122000.0,
        isPriceDynamic: true,
        priceSource: 'CUSTOM',
        sku: 'SILVER-500G',
        image: 'products/silver-500g.jpg',
        stockQuantity: 30,
        lowStockThreshold: 10,
        isActive: true,
        isFeatured: false,
        privateProducts: false,
        displayOrder: 3,
        createdAt: now,
        updatedAt: now,
        currentPrice: 122000.0,
      ),
      Product(
        id: 4,
        categoryId: 2,
        name: '1kg Silver Bar',
        slug: '1kg-silver-bar',
        description: 'Pure silver bar weighing 1 kilogram',
        type: 'silver',
        weight: 1000.0,
        weightInVori: 85.74,
        purity: '99.9%',
        karat: 'Fine',
        basePrice: 244000.0,
        isPriceDynamic: true,
        priceSource: 'SYSTEM',
        sku: 'SILVER-1KG',
        image: 'products/silver-1kg.jpg',
        stockQuantity: 15,
        lowStockThreshold: 3,
        isActive: true,
        isFeatured: false,
        privateProducts: true,
        displayOrder: 4,
        createdAt: now,
        updatedAt: now,
        currentPrice: 244000.0,
      ),
    ];
  }

  // Unified Metal API Methods

  // Get the latest rate for a product type (gold or silver)
  Future<Map<String, dynamic>> getLatestRate(String type) async {
    try {
      final response = await _apiService.get('${ApiConfig.baseUrl}/metal/rates/$type');

      if (response.containsKey('success') && response['success'] == true) {
        return response['rate'];
      } else {
        throw Exception('Failed to load $type rate: ${response['message']}');
      }
    } catch (e) {
      print('Error in getLatestRate: $e');
      throw Exception('Failed to load $type rate. Please check your connection and try again.');
    }
  }

  // Purchase a product
  Future<Map<String, dynamic>> purchaseProduct(int productId, int quantity, String paymentMethod) async {
    try {
      final response = await _apiService.post(
        '${ApiConfig.baseUrl}/metal/purchase',
        data: {
          'product_id': productId,
          'quantity': quantity,
          'payment_method': paymentMethod,
        },
      );

      if (response.containsKey('success') && response['success'] == true) {
        return response;
      } else {
        throw Exception('Failed to purchase product: ${response['message']}');
      }
    } catch (e) {
      print('Error in purchaseProduct: $e');
      throw Exception('Failed to purchase product. Please check your connection and try again.');
    }
  }

  // Get user's product purchases (gold or silver)
  Future<dynamic> getUserProducts(String type) async {
    try {
      final response = await _apiService.get('${ApiConfig.baseUrl}/metal/my/$type');

      if (type == 'gold') {
        return GoldCollection(
          goldItems: (response['gold_items'] as List).map((item) => GoldPurchase.fromJson(item)).toList(),
          totalBalance: double.tryParse(response['total_balance']?.toString() ?? '0.0') ?? 0.0,
          availableBalance: double.tryParse(response['available_balance']?.toString() ?? '0.0') ?? 0.0,
        );
      } else if (type == 'silver') {
        return SilverCollection.fromJson(response);
      } else {
        throw Exception('Invalid product type');
      }
    } catch (e) {
      print('Error in getUserProducts: $e');
      throw Exception('Failed to load $type data. Please check your connection and try again.');
    }
  }

  // Get product delivery information
  Future<Map<String, dynamic>> getProductDeliveryInfo(String type) async {
    try {
      final response = await _apiService.get('${ApiConfig.baseUrl}/metal/delivery-info/$type');

      if (response.containsKey('success') && response['success'] == true) {
        // Parse the response values with proper error handling
        final deliveredAmount = double.tryParse(response['delivered_' + type]?.toString() ?? '0.0') ?? 0.0;
        final inProgressAmount = double.tryParse(response['in_progress_' + type]?.toString() ?? '0.0') ?? 0.0;
        final availableAmount = double.tryParse(response['available_' + type]?.toString() ?? '0.0') ?? 0.0;
        final totalPurchased = double.tryParse(response['total_purchased']?.toString() ?? '0.0') ?? 0.0;

        // Log the values for debugging
        print('$type delivery info: delivered=$deliveredAmount, inProgress=$inProgressAmount, ' +
              'available=$availableAmount, totalPurchased=$totalPurchased');

        return {
          'delivered_' + type: deliveredAmount,
          'in_progress_' + type: inProgressAmount,
          'available_' + type: availableAmount,
          'total_purchased': totalPurchased,
        };
      } else {
        throw Exception('Failed to load $type delivery information: ${response['message']}');
      }
    } catch (e) {
      print('Error in getProductDeliveryInfo: $e');
      throw Exception('Failed to load $type delivery information. Please check your connection and try again.');
    }
  }
}
