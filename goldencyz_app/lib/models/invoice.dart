import 'dart:convert';

class Invoice {
  final int id;
  final String invoiceNumber;
  final String type;
  final double amount;
  final double goldAmount;
  final double silverAmount;
  final double totalAmount;
  final String status;
  final DateTime createdAt;
  final DateTime? paidAt;
  final String? paymentMethod;
  final Map<String, dynamic>? metadata;

  Invoice({
    required this.id,
    required this.invoiceNumber,
    required this.type,
    required this.amount,
    required this.goldAmount,
    this.silverAmount = 0.0,
    required this.totalAmount,
    required this.status,
    required this.createdAt,
    this.paidAt,
    this.paymentMethod,
    this.metadata,
  });

  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
      id: json['id'],
      invoiceNumber: json['invoice_number'],
      type: json['type'] ?? 'purchase',
      amount: double.parse(json['amount'].toString()),
      goldAmount: json.containsKey('gold_amount') ? double.parse(json['gold_amount'].toString()) : 0.0,
      silverAmount: json.containsKey('silver_amount') ? double.parse(json['silver_amount'].toString()) : 0.0,
      totalAmount: double.parse(json['total_amount'].toString()),
      status: json['status'],
      createdAt: DateTime.parse(json['created_at']),
      paidAt: json['paid_at'] != null ? DateTime.parse(json['paid_at']) : null,
      paymentMethod: json['payment_method'],
      metadata: json['metadata'],
    );
  }
}

class PurchaseInfo {
  final int id;
  final String transactionId;
  final double goldAmount;
  final double silverAmount;
  final double pricePerGram;
  final bool isGift;
  final double serviceCharge;
  final double platformFee;
  final String type; // 'gold' or 'silver'
  final List<Map<String, dynamic>>? items; // Added items field for product details

  PurchaseInfo({
    required this.id,
    required this.transactionId,
    required this.goldAmount,
    this.silverAmount = 0.0,
    required this.pricePerGram,
    required this.isGift,
    this.serviceCharge = 0.0,
    this.platformFee = 0.0,
    this.type = 'gold',
    this.items,
  });

  factory PurchaseInfo.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return PurchaseInfo(
        id: 0,
        transactionId: '',
        goldAmount: 0,
        pricePerGram: 0,
        isGift: false,
        serviceCharge: 0.0,
        platformFee: 0.0,
        type: 'gold',
      );
    }

    // Extract service charge and platform fee from tax_details if available
    double serviceCharge = 0.0;
    double platformFee = 0.0;

    if (json.containsKey('tax_details') && json['tax_details'] != null) {
      var taxDetails = json['tax_details'];
      if (taxDetails is String) {
        try {
          // Try to parse the string as JSON
          Map<String, dynamic> taxMap = jsonDecode(taxDetails);
          serviceCharge = double.tryParse(taxMap['service_charge']?.toString() ?? '0.0') ?? 0.0;
          platformFee = double.tryParse(taxMap['platform_fee']?.toString() ?? '0.0') ?? 0.0;
        } catch (e) {
          // Ignore parsing errors
        }
      } else if (taxDetails is Map) {
        serviceCharge = double.tryParse(taxDetails['service_charge']?.toString() ?? '0.0') ?? 0.0;
        platformFee = double.tryParse(taxDetails['platform_fee']?.toString() ?? '0.0') ?? 0.0;
      }
    }

    // Determine if this is a gold or silver purchase
    String type = 'gold';
    if (json.containsKey('type')) {
      type = json['type'];
    } else if (json.containsKey('silver_amount') &&
              (json['silver_amount'] != null && double.parse(json['silver_amount'].toString()) > 0)) {
      type = 'silver';
    } else if (json.containsKey('gold_amount') == false &&
              json.containsKey('silver_amount') == true) {
      type = 'silver';
    }

    // Extract gold and silver amounts with proper fallbacks
    double goldAmount = 0.0;
    double silverAmount = 0.0;

    if (json.containsKey('gold_amount') && json['gold_amount'] != null) {
      goldAmount = double.parse(json['gold_amount'].toString());
    } else if (json.containsKey('amount_of_gold') && json['amount_of_gold'] != null) {
      goldAmount = double.parse(json['amount_of_gold'].toString());
    }

    if (json.containsKey('silver_amount') && json['silver_amount'] != null) {
      silverAmount = double.parse(json['silver_amount'].toString());
    } else if (json.containsKey('amount_of_silver') && json['amount_of_silver'] != null) {
      silverAmount = double.parse(json['amount_of_silver'].toString());
    }

    // Parse items if available
    List<Map<String, dynamic>>? items;
    if (json.containsKey('items') && json['items'] != null) {
      items = List<Map<String, dynamic>>.from(json['items']);
    }

    return PurchaseInfo(
      id: json['id'] ?? 0, // Use 0 as default if id is null
      transactionId: json['transaction_id'] ?? '',
      goldAmount: goldAmount,
      silverAmount: silverAmount,
      pricePerGram: double.parse((json['price_per_gram'] ?? 0).toString()),
      isGift: json['is_gift'] ?? false,
      serviceCharge: serviceCharge,
      platformFee: platformFee,
      type: type,
      items: items,
    );
  }
}

class InvoiceDetails {
  final int id;
  final String invoiceNumber;
  final double amount;
  final double taxAmount;
  final double totalAmount;
  final String status;
  final String createdAt;
  final String? pdfPath;
  final PurchaseInfo? purchase;

  InvoiceDetails({
    required this.id,
    required this.invoiceNumber,
    required this.amount,
    required this.taxAmount,
    required this.totalAmount,
    required this.status,
    required this.createdAt,
    this.pdfPath,
    this.purchase,
  });

  factory InvoiceDetails.fromJson(Map<String, dynamic> json) {
    return InvoiceDetails(
      id: json['id'],
      invoiceNumber: json['invoice_number'],
      amount: double.parse(json['amount'].toString()),
      taxAmount: double.parse(json['tax_amount'].toString()),
      totalAmount: double.parse(json['total_amount'].toString()),
      status: json['status'],
      createdAt: json['created_at'],
      pdfPath: json['pdf_path'],
      purchase: PurchaseInfo.fromJson(json['purchase']),
    );
  }
}

class CompanyInfo {
  final String name;
  final String address;
  final String city;
  final String postalCode;
  final String country;
  final String email;
  final String phone;
  final String? bin;
  final String? tin;
  final String? mushak;
  final String? vatCountry;
  final String? govtOrg;
  final String? goldTradingLicense;
  final String? footerText;

  CompanyInfo({
    required this.name,
    required this.address,
    required this.city,
    required this.postalCode,
    required this.country,
    required this.email,
    required this.phone,
    this.bin,
    this.tin,
    this.mushak,
    this.vatCountry,
    this.govtOrg,
    this.goldTradingLicense,
    this.footerText,
  });

  factory CompanyInfo.fromJson(Map<String, dynamic> json) {
    return CompanyInfo(
      name: json['name'] ?? 'GoldencyZ',
      address: json['address'] ?? '15/D Haji Chinu Miah Road, Ring Road, Mohammadpur',
      city: json['city'] ?? 'Dhaka',
      postalCode: json['postal_code'] ?? '1217',
      country: json['country'] ?? 'Bangladesh',
      email: json['email'] ?? '<EMAIL>',
      phone: json['phone'] ?? '+8801770852040',
      bin: json['bin'],
      tin: json['tin'],
      mushak: json['mushak'],
      vatCountry: json['vat_country'],
      govtOrg: json['govt_org'],
      goldTradingLicense: json['gold_trading_license'],
      footerText: json['footer_text'],
    );
  }

  static CompanyInfo defaultInfo() {
    return CompanyInfo(
      name: 'GoldencyZ',
      address: '15/D Haji Chinu Miah Road, Ring Road, Mohammadpur',
      city: 'Dhaka',
      postalCode: '1217',
      country: 'Bangladesh',
      email: '<EMAIL>',
      phone: '+8801770852040',
      bin: '',
      tin: '',
      mushak: '',
      vatCountry: '',
      govtOrg: null,
      goldTradingLicense: '',
      footerText: 'Thank you for your purchase! For any inquiries, please contact our customer service.',
    );
  }
}

class InvoiceData {
  final InvoiceInfo invoice;
  final PurchaseInfo purchase;
  final PaymentInfo? payment;
  final CustomerInfo customer;
  final CompanyInfo company;
  final Map<String, dynamic>? deliveryInfo; // Added for delivery information
  final Map<String, dynamic>? delivery; // Added for delivery request details
  final List<Map<String, dynamic>>? associatedOrders; // Added for associated orders in delivery invoices

  InvoiceData({
    required this.invoice,
    required this.purchase,
    this.payment,
    required this.customer,
    required this.company,
    this.deliveryInfo,
    this.delivery,
    this.associatedOrders,
  });

  factory InvoiceData.fromJson(Map<String, dynamic> json) {
    // Handle company data which can be either a CompanyInfo object or a raw Map
    dynamic companyData = json['company'];

    // Handle purchase data which can be null or have null fields
    Map<String, dynamic>? purchaseData = json['purchase'] as Map<String, dynamic>?;

    // If purchase.id is null, create a default purchase with id=0
    if (purchaseData != null && purchaseData['id'] == null) {
      purchaseData['id'] = 0;
    }

    // Extract delivery info if available
    Map<String, dynamic>? deliveryInfo;
    if (json.containsKey('delivery_info') && json['delivery_info'] != null) {
      deliveryInfo = json['delivery_info'] as Map<String, dynamic>;
    }

    // Extract delivery request details if available
    Map<String, dynamic>? delivery;
    if (json.containsKey('delivery') && json['delivery'] != null) {
      delivery = json['delivery'] as Map<String, dynamic>;
    }

    // Add notes to invoice if available
    if (json.containsKey('invoice') &&
        json['invoice'] is Map<String, dynamic> &&
        !json['invoice'].containsKey('notes') &&
        json.containsKey('notes')) {
      (json['invoice'] as Map<String, dynamic>)['notes'] = json['notes'];
    }

    // Parse associated orders if available
    List<Map<String, dynamic>>? associatedOrders;
    if (json.containsKey('associated_orders') && json['associated_orders'] != null) {
      associatedOrders = List<Map<String, dynamic>>.from(json['associated_orders']);
    }

    return InvoiceData(
      invoice: InvoiceInfo.fromJson(json['invoice']),
      purchase: PurchaseInfo.fromJson(purchaseData),
      payment: json['payment'] != null ? PaymentInfo.fromJson(json['payment']) : null,
      customer: CustomerInfo.fromJson(json['customer']),
      company: companyData is Map<String, dynamic> ? CompanyInfo.fromJson(companyData) : CompanyInfo.defaultInfo(),
      deliveryInfo: deliveryInfo,
      delivery: delivery,
      associatedOrders: associatedOrders,
    );
  }
}

class InvoiceInfo {
  final int id;
  final String invoiceNumber;
  final String? mushakNumber;
  final String? fiscalYear;
  final String date;
  final String status;
  final double amount;
  final double taxAmount;
  final double vatAmount;
  final double vatPercentage;
  final double tdsAmount;
  final double tdsPercentage;
  final double totalAmount;
  final double goldAmount;
  final double silverAmount;
  final int? goldPurchaseId;
  final int? silverPurchaseId;
  final String invoiceType; // Added invoice type field
  final String? notes; // Added notes field for delivery information
  final Map<String, dynamic>? metadata; // Added metadata field for payment_status
  final int? couponId;
  final String? couponCode;
  final double discountAmount;
  final String? discountType;
  final double discountPercentage;
  final double? originalTotal;
  final List<Map<String, dynamic>>? items; // Added items field for product details

  InvoiceInfo({
    required this.id,
    required this.invoiceNumber,
    this.mushakNumber,
    this.fiscalYear,
    required this.date,
    required this.status,
    required this.amount,
    required this.taxAmount,
    this.vatAmount = 0.0,
    this.vatPercentage = 5.0,
    this.tdsAmount = 0.0,
    this.tdsPercentage = 0.25,
    required this.totalAmount,
    this.goldAmount = 0.0,
    this.silverAmount = 0.0,
    this.goldPurchaseId,
    this.silverPurchaseId,
    this.invoiceType = 'purchase', // Default to 'purchase' if not specified
    this.notes, // Notes field for delivery information
    this.metadata, // Metadata field for payment_status
    this.couponId,
    this.couponCode,
    this.discountAmount = 0.0,
    this.discountType,
    this.discountPercentage = 0.0,
    this.originalTotal,
    this.items, // Items field for product details
  });

  factory InvoiceInfo.fromJson(Map<String, dynamic> json) {
    // Determine invoice type
    String invoiceType = 'purchase'; // Default

    // Check if invoice_type is explicitly provided
    if (json.containsKey('invoice_type') && json['invoice_type'] != null) {
      invoiceType = json['invoice_type'];
    }
    // Otherwise check invoice number format
    else if (json.containsKey('invoice_number') && json['invoice_number'] != null) {
      String invoiceNumber = json['invoice_number'];
      if (invoiceNumber.startsWith('DEL-') || invoiceNumber.startsWith('INV-DEL')) {
        invoiceType = 'delivery';
      }
    }

    // Parse items if available
    List<Map<String, dynamic>>? items;
    if (json.containsKey('items') && json['items'] != null) {
      items = List<Map<String, dynamic>>.from(json['items']);
    }

    return InvoiceInfo(
      id: json['id'] ?? 0,
      invoiceNumber: json['invoice_number'] ?? '',
      mushakNumber: json['mushak_number'],
      fiscalYear: json['fiscal_year'],
      date: json['date'] ?? DateTime.now().toString().substring(0, 10),
      status: json['status'] ?? 'pending',
      amount: double.parse((json['amount'] ?? 0).toString()),
      taxAmount: double.parse((json['tax_amount'] ?? 0).toString()),
      vatAmount: json.containsKey('vat_amount') ? double.parse((json['vat_amount'] ?? 0).toString()) : 0.0,
      vatPercentage: json.containsKey('vat_percentage') ? double.parse((json['vat_percentage'] ?? 5.0).toString()) : 5.0,
      tdsAmount: json.containsKey('tds_amount') ? double.parse((json['tds_amount'] ?? 0).toString()) : 0.0,
      tdsPercentage: json.containsKey('tds_percentage') ? double.parse((json['tds_percentage'] ?? 0.25).toString()) : 0.25,
      totalAmount: double.parse((json['total_amount'] ?? 0).toString()),
      goldAmount: json.containsKey('amount_of_gold') ? double.parse((json['amount_of_gold'] ?? 0).toString()) :
                 (json.containsKey('gold_amount') ? double.parse((json['gold_amount'] ?? 0).toString()) : 0.0),
      silverAmount: json.containsKey('amount_of_silver') ? double.parse((json['amount_of_silver'] ?? 0).toString()) :
                   (json.containsKey('silver_amount') ? double.parse((json['silver_amount'] ?? 0).toString()) : 0.0),
      goldPurchaseId: json['gold_purchase_id'],
      silverPurchaseId: json['silver_purchase_id'],
      invoiceType: invoiceType,
      notes: json['notes'],
      metadata: json['metadata'] as Map<String, dynamic>?,
      couponId: json['coupon_id'],
      couponCode: json['coupon_code'],
      discountAmount: json.containsKey('discount_amount') ? double.parse((json['discount_amount'] ?? 0).toString()) : 0.0,
      discountType: json['discount_type'],
      discountPercentage: json.containsKey('discount_percentage') ? double.parse((json['discount_percentage'] ?? 0).toString()) : 0.0,
      originalTotal: json['original_total'] != null ? double.parse(json['original_total'].toString()) : null,
      items: items,
    );
  }
}

class PaymentInfo {
  final int id;
  final String method;
  final String status;
  final String date;

  PaymentInfo({
    required this.id,
    required this.method,
    required this.status,
    required this.date,
  });

  factory PaymentInfo.fromJson(Map<String, dynamic> json) {
    return PaymentInfo(
      id: json['id'] ?? 0,
      method: json['method'] ?? 'Unknown',
      status: json['status'] ?? 'pending',
      date: json['date'] ?? DateTime.now().toString().substring(0, 10),
    );
  }
}

class CustomerInfo {
  final String name;
  final String email;
  final String phone;
  final String address;
  final String city;

  CustomerInfo({
    required this.name,
    required this.email,
    required this.phone,
    this.address = '',
    this.city = '',
  });

  factory CustomerInfo.fromJson(Map<String, dynamic> json) {
    return CustomerInfo(
      name: json['name'] ?? 'Customer',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
    );
  }
}

class InvoiceSummary {
  final int id;
  final String invoiceNumber;
  final double amount;
  final double goldAmount;
  final double silverAmount;
  final double totalAmount;
  final String status;
  final String createdAt;
  final int? goldPurchaseId;
  final int? silverPurchaseId;
  final int? orderId;
  final int? deliveryRequestId;
  final int? couponId;
  final String? couponCode;
  final double discountAmount;
  final String? discountType;
  final double discountPercentage;
  final double? originalTotal;

  InvoiceSummary({
    required this.id,
    required this.invoiceNumber,
    required this.amount,
    this.goldAmount = 0.0,
    this.silverAmount = 0.0,
    required this.totalAmount,
    required this.status,
    required this.createdAt,
    this.goldPurchaseId,
    this.silverPurchaseId,
    this.orderId,
    this.deliveryRequestId,
    this.couponId,
    this.couponCode,
    this.discountAmount = 0.0,
    this.discountType,
    this.discountPercentage = 0.0,
    this.originalTotal,
  });

  factory InvoiceSummary.fromJson(Map<String, dynamic> json) {
    // Extract gold and silver amounts with proper fallbacks
    double goldAmount = 0.0;
    double silverAmount = 0.0;

    if (json.containsKey('gold_amount') && json['gold_amount'] != null) {
      goldAmount = double.parse(json['gold_amount'].toString());
    } else if (json.containsKey('amount_of_gold') && json['amount_of_gold'] != null) {
      goldAmount = double.parse(json['amount_of_gold'].toString());
    }

    if (json.containsKey('silver_amount') && json['silver_amount'] != null) {
      silverAmount = double.parse(json['silver_amount'].toString());
    } else if (json.containsKey('amount_of_silver') && json['amount_of_silver'] != null) {
      silverAmount = double.parse(json['amount_of_silver'].toString());
    }

    print('Invoice ${json['id']} gold amount: $goldAmount, silver amount: $silverAmount');

    return InvoiceSummary(
      id: json['id'],
      invoiceNumber: json['invoice_number'],
      amount: double.parse(json['amount'].toString()),
      goldAmount: goldAmount,
      silverAmount: silverAmount,
      totalAmount: double.parse(json['total_amount'].toString()),
      status: json['status'],
      createdAt: json['created_at'],
      goldPurchaseId: json['gold_purchase_id'],
      silverPurchaseId: json['silver_purchase_id'],
      orderId: json['order_id'],
      deliveryRequestId: json['delivery_request_id'],
      couponId: json['coupon_id'],
      couponCode: json['coupon_code'],
      discountAmount: json.containsKey('discount_amount') ? double.parse((json['discount_amount'] ?? 0).toString()) : 0.0,
      discountType: json['discount_type'],
      discountPercentage: json.containsKey('discount_percentage') ? double.parse((json['discount_percentage'] ?? 0).toString()) : 0.0,
      originalTotal: json['original_total'] != null ? double.parse(json['original_total'].toString()) : null,
    );
  }
}
