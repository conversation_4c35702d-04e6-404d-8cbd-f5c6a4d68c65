class Product {
  final int id;
  final int categoryId;
  final String name;
  final String slug;
  final String? description;
  final String type; // 'gold' or 'silver'
  final double weight;
  final double? weightInVori;
  final String? purity;
  final String? karat;
  final double? basePrice;
  final bool isPriceDynamic;
  final String priceSource; // 'BAJUS', 'CUSTOM', or 'SYSTEM'
  final String sku;
  final String? image;
  final int stockQuantity;
  final int lowStockThreshold;
  final bool isActive;
  final bool isFeatured;
  final bool privateProducts;
  final int displayOrder;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ProductCategory? category;
  final double currentPrice;

  Product({
    required this.id,
    required this.categoryId,
    required this.name,
    required this.slug,
    this.description,
    required this.type,
    required this.weight,
    this.weightInVori,
    this.purity,
    this.karat,
    this.basePrice,
    required this.isPriceDynamic,
    required this.priceSource,
    required this.sku,
    this.image,
    required this.stockQuantity,
    required this.lowStockThreshold,
    required this.isActive,
    required this.isFeatured,
    required this.privateProducts,
    required this.displayOrder,
    required this.createdAt,
    required this.updatedAt,
    this.category,
    required this.currentPrice,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'],
      categoryId: json['category_id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      type: json['type'],
      weight: double.parse(json['weight'].toString()),
      weightInVori: json['weight_in_vori'] != null ? double.parse(json['weight_in_vori'].toString()) : null,
      purity: json['purity'],
      karat: json['karat'],
      basePrice: json['base_price'] != null ? double.parse(json['base_price'].toString()) : null,
      isPriceDynamic: json['is_price_dynamic'] == 1 || json['is_price_dynamic'] == true,
      priceSource: json['price_source'] ?? 'BAJUS',
      sku: json['sku'],
      image: json['image'],
      stockQuantity: json['stock_quantity'] ?? 0,
      lowStockThreshold: json['low_stock_threshold'] ?? 5,
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      isFeatured: json['is_featured'] == 1 || json['is_featured'] == true,
      privateProducts: json['private_products'] == 1 || json['private_products'] == true,
      displayOrder: json['display_order'] ?? 0,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : DateTime.now(),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : DateTime.now(),
      category: json['category'] != null ? ProductCategory.fromJson(json['category']) : null,
      currentPrice: json['current_price'] != null ? double.parse(json['current_price'].toString()) : (json['price'] != null ? double.parse(json['price'].toString()) : 0.0),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category_id': categoryId,
      'name': name,
      'slug': slug,
      'description': description,
      'type': type,
      'weight': weight,
      'weight_in_vori': weightInVori,
      'purity': purity,
      'karat': karat,
      'base_price': basePrice,
      'is_price_dynamic': isPriceDynamic,
      'price_source': priceSource,
      'sku': sku,
      'image': image,
      'stock_quantity': stockQuantity,
      'low_stock_threshold': lowStockThreshold,
      'is_active': isActive,
      'is_featured': isFeatured,
      'private_products': privateProducts,
      'display_order': displayOrder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'category': category?.toJson(),
      'current_price': currentPrice,
    };
  }

  bool get isGold => type == 'gold';
  bool get isSilver => type == 'silver';
  bool get isInStock => stockQuantity > 0;
  bool get isLowStock => stockQuantity <= lowStockThreshold;
  String get formattedWeight => '$weight g';
  String get formattedPrice => '৳ ${currentPrice.toStringAsFixed(2)}';
  String get imageUrl {
    // First try to load from network if image path is available
    if (image != null && image!.isNotEmpty) {
      // Check if the image path already contains http or https
      if (image!.startsWith('http://') || image!.startsWith('https://')) {
        return image!;
      }
      // Otherwise, prepend the storage URL
      return 'https://goldencyz.com/storage/$image';
    }

    // If no image is available, use local fallback images
    return isGold ? 'assets/images/gold_bar.png' : 'assets/images/silver_bar.png';
  }
}

class ProductCategory {
  final int id;
  final String name;
  final String slug;
  final String? description;
  final String? icon;
  final bool isActive;
  final int displayOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProductCategory({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.icon,
    required this.isActive,
    required this.displayOrder,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ProductCategory.fromJson(Map<String, dynamic> json) {
    return ProductCategory(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      icon: json['icon'],
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      displayOrder: json['display_order'] ?? 0,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : DateTime.now(),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'icon': icon,
      'is_active': isActive,
      'display_order': displayOrder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
