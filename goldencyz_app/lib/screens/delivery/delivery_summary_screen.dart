import 'package:flutter/material.dart';
import 'package:goldencyz_app/constants.dart';
import 'package:goldencyz_app/models/coupon.dart';
import 'package:goldencyz_app/models/gold_purchase.dart';
import 'package:goldencyz_app/screens/delivery/delivery_success_screen.dart';
import 'package:goldencyz_app/services/coupon_service.dart';
import 'package:goldencyz_app/services/service_provider.dart';
import 'package:goldencyz_app/widgets/coupon_input.dart';
import 'package:intl/intl.dart';

class DeliverySummaryScreen extends StatefulWidget {
  final Map<String, String> deliveryAddress;
  final double deliveryCharge;
  final GoldCollection goldCollection;
  final double availableGold;
  final double availableSilver;

  const DeliverySummaryScreen({
    super.key,
    required this.deliveryAddress,
    required this.deliveryCharge,
    required this.goldCollection,
    this.availableGold = 0.0,
    this.availableSilver = 0.0,
  });

  @override
  State<DeliverySummaryScreen> createState() => _DeliverySummaryScreenState();
}

class _DeliverySummaryScreenState extends State<DeliverySummaryScreen> {
  final ServiceProvider _serviceProvider = ServiceProvider();
  bool _isProcessing = false;
  String? _errorMessage;
  GoldCollection? _latestGoldCollection;
  double _latestGoldAmount = 0.0;
  double _latestSilverAmount = 0.0;

  // Coupon related
  CouponValidation? _appliedCoupon;
  double _discountAmount = 0;
  double _originalDeliveryCharge = 0;
  double _finalDeliveryCharge = 0;

  @override
  void initState() {
    super.initState();
    _originalDeliveryCharge = widget.deliveryCharge;
    _finalDeliveryCharge = widget.deliveryCharge;
    _refreshAssetData();
  }

  void _handleCouponApplied(CouponValidation couponValidation) {
    setState(() {
      _appliedCoupon = couponValidation;
      if (couponValidation.success && couponValidation.discountAmount != null) {
        _discountAmount = couponValidation.discountAmount!;
        _finalDeliveryCharge = couponValidation.finalAmount ?? (_originalDeliveryCharge - _discountAmount);
      }
    });
  }

  void _handleCouponRemoved() {
    setState(() {
      _appliedCoupon = null;
      _discountAmount = 0;
      _finalDeliveryCharge = _originalDeliveryCharge;
    });
  }

  Future<void> _refreshAssetData() async {
    try {
      // Get latest gold data for backward compatibility
      final latestGoldData = await _serviceProvider.goldService.getUserGold();

      // Get combined asset info from the new unified endpoint
      final combinedAssetInfo = await _serviceProvider.activeUnifiedDeliveryService.getCombinedAssetInfo();

      // Extract available gold and silver amounts
      final availableGold = combinedAssetInfo['assets']['gold']['available'] ?? 0.0;
      final availableSilver = combinedAssetInfo['assets']['silver']['available'] ?? 0.0;

      // Get delivery requirements
      final minDeliveryAmount = combinedAssetInfo['delivery']['min_delivery_amount'] ?? 1.0;
      // Check if address and city are present to determine KYC status
      final hasAddress = widget.deliveryAddress['address_line1'] != null && widget.deliveryAddress['address_line1']!.isNotEmpty;
      final hasCity = widget.deliveryAddress['district'] != null && widget.deliveryAddress['district']!.isNotEmpty;
      // Check if KYC is verified, this checking is temporary, we will remove this after KYC is mandatory
      final isKycVerified = hasAddress && hasCity;
      final canRequestDelivery = combinedAssetInfo['delivery']['can_request'] ?? false;

      if (mounted) {
        setState(() {
          _latestGoldCollection = latestGoldData;
          _latestGoldAmount = availableGold;
          _latestSilverAmount = availableSilver;
        });

        // If delivery can't be requested, show appropriate warning
        if (!canRequestDelivery) {
          if (!isKycVerified) {
            setState(() {
              _errorMessage = 'You need to complete identity verification before requesting delivery.';
            });
          } else if (availableGold + availableSilver < minDeliveryAmount) {
            setState(() {
              _errorMessage = 'You need at least ${minDeliveryAmount.toStringAsFixed(1)} gram of metal (gold or silver) to request a delivery.';
            });
          }
        }
      }
    } catch (e) {
      print('Error refreshing asset data: $e');
      if (mounted) {
        // Fallback to old method if the new endpoint fails
        try {
          // Get latest gold delivery info
          final goldDeliveryInfo = await _serviceProvider.goldService.getGoldDeliveryInfo();
          final availableGold = goldDeliveryInfo['available_gold'] ?? 0.0;

          // Get latest silver delivery info
          double availableSilver = 0.0;
          try {
            final silverDeliveryInfo = await _serviceProvider.silverService.getSilverDeliveryInfo();
            availableSilver = silverDeliveryInfo['available_silver'] ?? 0.0;
          } catch (e) {
            print('Error fetching silver delivery info: $e');
          }

          setState(() {
            _latestGoldAmount = availableGold;
            _latestSilverAmount = availableSilver;

            // If there's no available metal, show a warning
            if (availableGold + availableSilver < 1.0) {
              _errorMessage = 'You need at least 1 gram of metal (gold or silver) to request a delivery.';
            }
          });
        } catch (fallbackError) {
          print('Error in fallback asset data refresh: $fallbackError');
        }
      }
    }
  }

  Future<void> _initiateDeliveryRequest() async {
    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    try {
      // Double-check that there's available metal to deliver
      final totalAvailableMetal = _latestGoldAmount + _latestSilverAmount;
      if (totalAvailableMetal < 1.0) {
        throw Exception('You need at least 1 gram of metal (gold or silver) to request a delivery.');
      }

      // Generate a transaction ID for the delivery reference
      final transactionId = 'DEL_${DateTime.now().millisecondsSinceEpoch}';

      // Directly create the delivery request without payment screen
      _createDeliveryRequest(transactionId, _appliedCoupon?.coupon?.code);
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString().contains('at least 1 gram')
              ? 'You need at least 1 gram of metal (gold or silver) to request a delivery.'
              : 'Failed to initiate delivery request: ${e.toString()}';
          _isProcessing = false;
        });
      }
    }
  }

  Future<void> _createDeliveryRequest(String transactionId, String? couponCode) async {
    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    // Double-check that there's available metal to deliver before proceeding
    try {
      final totalAvailableMetal = _latestGoldAmount + _latestSilverAmount;
      if (totalAvailableMetal < 1.0) {
        throw Exception('You need at least 1 gram of metal (gold or silver) to request a delivery.');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString().contains('at least 1 gram')
              ? 'You need at least 1 gram of metal (gold or silver) to request a delivery.'
              : 'Failed to verify metal balance: ${e.toString()}';
          _isProcessing = false;
        });
        return;
      }
    }

    try {
      // Combine address fields into a single string for the API
      final fullAddress = [
        widget.deliveryAddress['address_line1'],
        widget.deliveryAddress['address_line2'],
        widget.deliveryAddress['area'],
        widget.deliveryAddress['thana'],
        widget.deliveryAddress['district'],
      ].where((item) => item != null && item.isNotEmpty).join(', ');

      print('Creating delivery request with transaction ID: $transactionId');
      print('Full address: $fullAddress');

      // Call the API to create delivery request for all metals using the unified delivery service
      final response = await _serviceProvider.activeUnifiedDeliveryService.requestDeliveryForAllMetals(
        deliveryAddress: fullAddress,
        city: widget.deliveryAddress['district'] ?? '',
        contactPhone: widget.deliveryAddress['alternative_phone'] ?? '',
        deliveryNotes: 'Reference ID: $transactionId. Delivery fee of ${_finalDeliveryCharge.toStringAsFixed(2)} BDT will be collected during delivery. Requesting delivery of all available metals.',
        couponCode: couponCode,
      );

      print('Delivery request response: $response');

      // Check if the response contains the expected data
      if (response.containsKey('delivery') && response['delivery'] != null) {
        final deliveryId = response['delivery']['id'];
        print('Delivery ID from response: $deliveryId');

        if (mounted) {
          // Navigate to success screen
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => DeliverySuccessScreen(
                transactionId: transactionId,
                deliveryId: deliveryId,
              ),
            ),
          );
        }
      } else {
        // Handle unexpected response format
        throw Exception('Invalid response format: $response');
      }
    } catch (e) {
      print('Error creating delivery request: $e');
      if (mounted) {
        // Check if this is a "No available metal" error
        if (e.toString().contains('at least 1 gram')) {
          setState(() {
            _errorMessage = 'You need at least 1 gram of metal (gold or silver) to request a delivery.';
            _isProcessing = false;
          });

          // Show a more user-friendly message for this specific error
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_errorMessage!),
              backgroundColor: Colors.amber[700],
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: 'OK',
                textColor: Colors.white,
                onPressed: () {
                  Navigator.of(context).pop(); // Go back to previous screen
                },
              ),
            ),
          );
        } else if (e.toString().contains('You already have a pending delivery request')) {
          // Handle the case where user already has a pending delivery request
          // Extract the detailed error message
          String errorMsg = e.toString().replaceAll('Exception: ', '');

          // Format the error message to be more user-friendly
          if (errorMsg.contains('ID:') && errorMsg.contains('Status:')) {
            // This is the detailed error message with delivery information
            setState(() {
              _errorMessage = errorMsg;
              _isProcessing = false;
            });
          } else {
            // This is a generic error message
            setState(() {
              _errorMessage = 'You already have a pending delivery request. Please check your delivery status.';
              _isProcessing = false;
            });
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('You already have a pending delivery request. Please check your delivery status.'),
              backgroundColor: Colors.amber[700],
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: 'View Status',
                textColor: Colors.white,
                onPressed: () {
                  // Navigate to delivery status screen
                  Navigator.of(context).pop(); // Go back to previous screen
                  // Navigate to the delivery status screen
                  Navigator.of(context).pushNamed('/deliveries');
                },
              ),
            ),
          );
        } else {
          // Handle other errors
          setState(() {
            _errorMessage = 'Failed to create delivery request: ${e.toString()}';
            _isProcessing = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_errorMessage!),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: () => _createDeliveryRequest(transactionId, _appliedCoupon?.coupon?.code),
              ),
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(
      locale: 'en_BD',
      symbol: '৳',
      decimalDigits: 2,
    );

    // Use the latest data if available, otherwise use the data from the widget
    final goldAmount = _latestGoldAmount > 0 ? _latestGoldAmount : widget.availableGold;
    final silverAmount = _latestSilverAmount > 0 ? _latestSilverAmount : widget.availableSilver;
    final totalMetalAmount = goldAmount + silverAmount;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Delivery Summary'),
        backgroundColor: AppColors.darkEliteGreen,
        foregroundColor: AppColors.golden,
      ),
      body: _isProcessing
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _errorMessage!.contains('at least 1 gram')
                              ? Icons.info_outline
                              : _errorMessage!.contains('pending delivery request')
                                  ? Icons.pending_actions
                                  : Icons.error_outline,
                          size: 64,
                          color: _errorMessage!.contains('at least 1 gram')
                              ? Colors.amber.withOpacity(0.7)
                              : _errorMessage!.contains('pending delivery request')
                                  ? Colors.blue.withOpacity(0.7)
                                  : Colors.red.withOpacity(0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _errorMessage!,
                          style: TextStyle(
                            color: _errorMessage!.contains('at least 1 gram')
                                ? Colors.amber[800]
                                : _errorMessage!.contains('pending delivery request')
                                    ? Colors.blue[800]
                                    : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        if (_errorMessage!.contains('at least 1 gram'))
                          const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.0),
                            child: Text(
                              'Check your delivery status for more information or purchase more gold or silver.',
                              style: TextStyle(fontSize: 14),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        if (_errorMessage!.contains('pending delivery request'))
                          const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.0),
                            child: Text(
                              'You can check the status of your pending delivery in the Deliveries section of your account.',
                              style: TextStyle(fontSize: 14),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.golden,
                            foregroundColor: AppColors.darkEliteGreen,
                          ),
                          child: const Text('Go Back'),
                        ),
                      ],
                    ),
                  ),
                )
              : ListView(
                  padding: const EdgeInsets.all(16),
                  children: [
                    // Metal balance info
                    if (goldAmount > 0)
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.darkEliteGreen,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: AppColors.golden.withOpacity(0.5),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Available Gold to be Delivered',
                              style: TextStyle(
                                color: AppColors.golden,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '${goldAmount.toStringAsFixed(3)} g',
                              style: const TextStyle(
                                color: AppColors.golden,
                                fontWeight: FontWeight.bold,
                                fontSize: 24,
                              ),
                            ),
                          ],
                        ),
                      ),

                    if (goldAmount > 0 && silverAmount > 0)
                      const SizedBox(height: 16),

                    // Silver balance info
                    if (silverAmount > 0)
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.darkEliteGreen,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: AppColors.silverPrimary.withOpacity(0.5),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Available Silver to be Delivered',
                              style: TextStyle(
                                color: AppColors.silverPrimary,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '${silverAmount.toStringAsFixed(3)} g',
                              style: const TextStyle(
                                color: AppColors.silverPrimary,
                                fontWeight: FontWeight.bold,
                                fontSize: 24,
                              ),
                            ),
                          ],
                        ),
                      ),

                    const SizedBox(height: 24),
                    const Text(
                      'Delivery Address',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.golden,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Address summary
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.surfaceLighter,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AppColors.cardBorderSubtle,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (widget.deliveryAddress['address_line1'] != null && widget.deliveryAddress['address_line1']!.isNotEmpty)
                            Text(
                              widget.deliveryAddress['address_line1']!,
                              style: const TextStyle(
                                color: AppColors.textDark,
                                fontSize: 16,
                              ),
                            ),
                          if (widget.deliveryAddress['address_line2'] != null && widget.deliveryAddress['address_line2']!.isNotEmpty) ...[
                            const SizedBox(height: 4),
                            Text(
                              widget.deliveryAddress['address_line2']!,
                              style: const TextStyle(
                                color: AppColors.textDark,
                                fontSize: 16,
                              ),
                            ),
                          ],
                          const SizedBox(height: 4),
                          Text(
                            [
                              widget.deliveryAddress['area'],
                              widget.deliveryAddress['thana'],
                              widget.deliveryAddress['district'],
                            ].where((item) => item != null && item.isNotEmpty).join(', '),
                            style: const TextStyle(
                              color: AppColors.textDark,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Phone: ${widget.deliveryAddress['alternative_phone']}',
                            style: const TextStyle(
                              color: AppColors.textDark,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),
                    const Text(
                      'Delivery Fee',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.golden,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Delivery fee summary
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.surfaceLighter,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AppColors.cardBorderSubtle,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Delivery Fee',
                                style: TextStyle(
                                  color: AppColors.textDark,
                                  fontSize: 16,
                                ),
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  if (_discountAmount > 0) ...[
                                    Text(
                                      'Original: ${currencyFormat.format(_originalDeliveryCharge)}',
                                      style: TextStyle(
                                        color: AppColors.textMedium,
                                        fontSize: 14,
                                        decoration: TextDecoration.lineThrough,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    if (_appliedCoupon?.coupon?.code != null)
                                      Text(
                                        'Coupon: ${_appliedCoupon!.coupon!.code}',
                                        style: const TextStyle(
                                          color: Colors.green,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    const SizedBox(height: 4),
                                  ],
                                  Text(
                                    currencyFormat.format(_finalDeliveryCharge),
                                    style: const TextStyle(
                                      color: AppColors.textDark,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Delivery fee will be collected in cash at the time of delivery.',
                            style: TextStyle(
                              color: AppColors.textMedium,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),

                          // Add coupon input
                          const SizedBox(height: 16),
                          CouponInput(
                            type: 'delivery',
                            amount: _originalDeliveryCharge,
                            onCouponApplied: _handleCouponApplied,
                            onCouponRemoved: _handleCouponRemoved,
                            initialCoupon: _appliedCoupon,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Confirm button
                    ElevatedButton(
                      onPressed: totalMetalAmount >= 1.0 ? _initiateDeliveryRequest : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.golden,
                        foregroundColor: AppColors.darkEliteGreen,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        disabledBackgroundColor: AppColors.golden.withOpacity(0.3),
                        disabledForegroundColor: AppColors.darkEliteGreen.withOpacity(0.5),
                      ),
                      child: const Text(
                        'Confirm Delivery Request',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Cancel button
                    OutlinedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.golden,
                        side: const BorderSide(color: AppColors.golden),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
    );
  }
}
