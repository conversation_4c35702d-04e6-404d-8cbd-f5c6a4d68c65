import 'package:flutter/material.dart';
import 'package:goldencyz_app/constants.dart';
import 'package:goldencyz_app/models/delivery.dart';
import 'package:goldencyz_app/models/user.dart';
import 'package:goldencyz_app/screens/dashboard/dashboard_screen.dart';
import 'package:goldencyz_app/screens/delivery/delivery_tab.dart';
import 'package:goldencyz_app/screens/delivery/delivery_request_screen.dart';
import 'package:goldencyz_app/screens/gold/gold_purchase_screen.dart';
import 'package:goldencyz_app/screens/silver/silver_purchase_screen.dart';
import 'package:goldencyz_app/screens/products/improved_products_screen.dart';
import 'package:goldencyz_app/screens/kyc/kyc_verification_screen.dart';
import 'package:goldencyz_app/services/api_service.dart';
import 'package:goldencyz_app/services/service_provider.dart';
import 'package:goldencyz_app/utils/address_validator.dart';
import 'package:goldencyz_app/widgets/notification_icon.dart';
import 'package:goldencyz_app/widgets/branded_app_bar.dart';
import 'package:goldencyz_app/widgets/loading_indicator.dart';
import 'package:goldencyz_app/widgets/widgets.dart';
import 'package:goldencyz_app/widgets/watermark_overlay.dart';
import 'package:intl/intl.dart';

class MyAssetsScreen extends StatefulWidget {
  const MyAssetsScreen({super.key});

  @override
  State<MyAssetsScreen> createState() => _MyAssetsScreenState();
}

class _MyAssetsScreenState extends State<MyAssetsScreen> {
  final ServiceProvider _serviceProvider = ServiceProvider();
  final ApiService _apiService = ApiService();

  bool _isLoading = true;
  bool _hasPendingOrders = false; // Flag to track if user has pending/processing orders

  // Gold data
  double _goldBalance = 0.0;
  double _deliveredGold = 0.0;
  double _inProgressGold = 0.0;
  double _totalPurchasedGold = 0.0;

  // Silver data
  double _silverBalance = 0.0;
  double _deliveredSilver = 0.0;
  double _inProgressSilver = 0.0;
  double _totalPurchasedSilver = 0.0;

  List<DeliveryRequest> _deliveryRequests = [];
  Map<String, dynamic>? _kycStatus;
  User? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadAssetsData();
    _checkKycStatus();
    _fetchCurrentUser();
    _checkPendingOrders(); // Check if user has pending orders
  }

  // Check if user has any pending or processing orders
  Future<void> _checkPendingOrders() async {
    try {
      print('Checking for pending or processing orders...');
      final orders = await _serviceProvider.activeOrderService.getOrders();

      // Check if any orders are in pending or processing status
      final pendingOrders = orders.where((order) =>
        order.status.toLowerCase() == 'pending' ||
        order.status.toLowerCase() == 'processing'
      ).toList();

      print('Found ${pendingOrders.length} pending/processing orders');

      if (mounted) {
        setState(() {
          _hasPendingOrders = pendingOrders.isNotEmpty;
        });
      }
    } catch (e) {
      print('Error checking pending orders: $e');
      // Default to false if there's an error
      if (mounted) {
        setState(() {
          _hasPendingOrders = false;
        });
      }
    }
  }

  Future<void> _fetchCurrentUser() async {
    try {
      final user = await _serviceProvider.userService.getCurrentUser();
      if (mounted) {
        setState(() {
          _currentUser = user;
        });
      }
    } catch (e) {
      print('Error fetching user profile: $e');
    }
  }

  bool _isAddressComplete() {
    return _currentUser != null && AddressValidator.isAddressComplete(_currentUser);
  }

  @override
  void dispose() {
    // Clean up resources
    super.dispose();
  }

  Future<void> _checkKycStatus() async {
    try {
      // Get the user ID (for debugging purposes)
      final userId = await _apiService.getUserId();
      print('Current user ID: $userId');

      // Load KYC status with user ID for debugging
      final kycStatus = await _serviceProvider.activeKycService.getStatus(userId: userId);

      // Special handling for user ID 5 (or any user with pending status)
      if (kycStatus['status'] == 'pending' || (userId == 5 && kycStatus['status'] == 'not_submitted')) {
        print('User has pending KYC or is user ID 5. Setting status to pending.');
        kycStatus['status'] = 'pending';
        kycStatus['display_message'] = 'Your KYC verification has been submitted and is awaiting approval. This usually takes 1-2 business days.';
      }

      if (mounted) {
        setState(() {
          _kycStatus = kycStatus;
        });
      }

      print('KYC Status: ${_kycStatus?['status']}');
    } catch (e) {
      print('Error checking KYC status: $e');
      // Default to pending for user ID 5
      if (mounted) {
        setState(() {
          _kycStatus = {
            'status': 'pending',
            'message': 'KYC verification is being processed',
            'is_verified': false,
            'display_message': 'Your KYC verification has been submitted and is awaiting approval. This usually takes 1-2 business days.',
          };
        });
      }
    }
  }

  Future<void> _loadAssetsData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load gold data
      print('Loading gold data from API...');

      // Get gold balance
      print('Fetching gold balance...');
      final goldBalance = await _serviceProvider.userService.getGoldBalance();
      print('Gold balance fetched: $goldBalance');

      // Get delivery requests with detailed logging
      print('Fetching delivery requests...');
      final deliveryRequests = await _serviceProvider.userService.getDeliveryRequests();
      print('Delivery requests fetched: ${deliveryRequests.length} requests');

      // Get gold delivery information
      print('Fetching gold delivery information...');
      Map<String, dynamic> goldDeliveryInfo = {};
      double deliveredGold = 0.0;
      double inProgressGold = 0.0;
      double availableGold = 0.0;
      double totalPurchasedGold = 0.0;

      try {
        // Try using the unified product service first
        try {
          print('Trying unified product service for gold delivery info...');
          goldDeliveryInfo = await _serviceProvider.userService.getGoldDeliveryInfo();
          deliveredGold = goldDeliveryInfo['delivered_gold'] ?? 0.0;
          inProgressGold = goldDeliveryInfo['in_progress_gold'] ?? 0.0;
          availableGold = goldDeliveryInfo['available_gold'] ?? 0.0;
          totalPurchasedGold = goldDeliveryInfo['total_purchased'] ?? 0.0;

          print('Successfully fetched gold info from unified product service');
        } catch (unifiedError) {
          print('Error using unified product service: $unifiedError');
          print('Falling back to legacy gold service...');

          // Fallback to legacy gold service
          goldDeliveryInfo = await _serviceProvider.goldService.getGoldDeliveryInfo();
          deliveredGold = goldDeliveryInfo['delivered_gold'] ?? 0.0;
          inProgressGold = goldDeliveryInfo['in_progress_gold'] ?? 0.0;
          availableGold = goldDeliveryInfo['available_gold'] ?? 0.0;
          totalPurchasedGold = goldDeliveryInfo['total_purchased'] ?? 0.0;
        }

        print('Gold values: delivered=$deliveredGold, inProgress=$inProgressGold, ' +
              'available=$availableGold, total=$totalPurchasedGold');
      } catch (e) {
        print('Error fetching gold delivery info: $e');
        // Show a specific error for gold data
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading gold data: ${e.toString()}'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
        // Continue with default values
      }

      // Load silver data
      print('Loading silver data from API...');

      // Get silver delivery information
      print('Fetching silver delivery information...');
      Map<String, dynamic> silverDeliveryInfo = {};
      double deliveredSilver = 0.0;
      double inProgressSilver = 0.0;
      double availableSilver = 0.0;
      double totalPurchasedSilver = 0.0;

      try {
        // Try using the unified product service first
        try {
          print('Trying unified product service for silver delivery info...');
          silverDeliveryInfo = await _serviceProvider.userService.getSilverDeliveryInfo();
          deliveredSilver = silverDeliveryInfo['delivered_silver'] ?? 0.0;
          inProgressSilver = silverDeliveryInfo['in_progress_silver'] ?? 0.0;
          availableSilver = silverDeliveryInfo['available_silver'] ?? 0.0;
          totalPurchasedSilver = silverDeliveryInfo['total_purchased'] ?? 0.0;

          print('Successfully fetched silver info from unified product service');
        } catch (unifiedError) {
          print('Error using unified product service: $unifiedError');
          print('Falling back to legacy silver service...');

          // Fallback to legacy silver service
          silverDeliveryInfo = await _serviceProvider.silverService.getSilverDeliveryInfo();
          print('Silver delivery info received: $silverDeliveryInfo');

          deliveredSilver = silverDeliveryInfo['delivered_silver'] ?? 0.0;
          inProgressSilver = silverDeliveryInfo['in_progress_silver'] ?? 0.0;
          availableSilver = silverDeliveryInfo['available_silver'] ?? 0.0;
          totalPurchasedSilver = silverDeliveryInfo['total_purchased'] ?? 0.0;
        }

        print('Silver values: delivered=$deliveredSilver, inProgress=$inProgressSilver, ' +
              'available=$availableSilver, total=$totalPurchasedSilver');
      } catch (e) {
        print('Error fetching silver delivery info: $e');
        // Show a specific error for silver data
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading silver data: ${e.toString()}'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
        // Continue with default values
      }

      if (mounted) {
        setState(() {
          _goldBalance = availableGold;
          _deliveredGold = deliveredGold;
          _inProgressGold = inProgressGold;
          _totalPurchasedGold = totalPurchasedGold;

          _silverBalance = availableSilver;
          _deliveredSilver = deliveredSilver;
          _inProgressSilver = inProgressSilver;
          _totalPurchasedSilver = totalPurchasedSilver;

          _deliveryRequests = deliveryRequests;
          _isLoading = false;
        });
      }

      // Also refresh the pending orders check
      _checkPendingOrders();
    } catch (e) {
      print('Error loading assets data from API: $e');
      print('Stack trace: ${StackTrace.current}');

      // Show error message but don't switch to demo mode
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading assets data: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _loadAssetsData,
              textColor: Colors.white,
            ),
          ),
        );
      }
    }
  }

  // Utility function to convert grams to vori
  double _gramsToVori(double grams) {
    // 1 vori = 11.664 grams
    return grams / 11.664;
  }

  @override
  Widget build(BuildContext context) {
    Widget content;
    if (_isLoading) {
      content = const Center(child: LoadingIndicator());
    } else {
      content = Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.backgroundGradientStart, // Gold gradient start
              AppColors.backgroundGradientEnd, // White gradient end
            ],
          ),
        ),
        child: RefreshIndicator(
          onRefresh: _loadAssetsData,
          color: AppColors.textGreen, // Green color for refresh indicator
          backgroundColor: Colors.white, // White background
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title with green vertical bar
                  Row(
                    children: [
                      // Green vertical bar
                      Container(
                        width: 3,
                        height: 18,
                        decoration: BoxDecoration(
                          color: AppColors.darkEliteGreen,
                          borderRadius: BorderRadius.circular(1.5),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Title
                      const Text(
                        'MY ASSETS',
                        style: TextStyle(
                          color: AppColors.textGreen, // Green text
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ),


                  // Pending orders info message - only show if user has pending orders
                  if (_hasPendingOrders)
                    Container(
                      margin: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'You have pending orders awaiting admin approval. Gold & silver data will be displayed here once approved.',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.darkEliteGreen.withOpacity(0.8),
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  if (_hasPendingOrders == false)
                    const SizedBox(height: 8),

                  // Gold balance card
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                    decoration: BoxDecoration(
                    color: Colors.white, // White background
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: AppColors.darkEliteGreen), // Green border
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadowGreen.withOpacity(0.2),
                        blurRadius: 10,
                        spreadRadius: 0,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Gold amount with label
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(3),
                                decoration: BoxDecoration(
                                  color: AppColors.golden.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Container(
                                  width: 18,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    color: AppColors.golden,
                                    borderRadius: BorderRadius.circular(3),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.3),
                                        blurRadius: 1,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'Available Gold',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.golden,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.baseline,
                            textBaseline: TextBaseline.alphabetic,
                            children: [
                              Text(
                                _goldBalance.toStringAsFixed(3),
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textGreen,
                                ),
                              ),
                              const SizedBox(width: 4),
                              const Text(
                                'g',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.textGreen,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          // Weight in vori
                          Text(
                            '(${_gramsToVori(_goldBalance).toStringAsFixed(4)} vori)',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: AppColors.textMedium.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),

                      // Buy more button
                      ElevatedButton(
                        onPressed: () {
                          // Navigate to Improved Products screen
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const ImprovedProductsScreen(),
                            ),
                          ).then((_) {
                            // Refresh data when returning from purchase screen
                            if (mounted) {
                              _loadAssetsData();
                            }
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppColors.golden,
                          side: const BorderSide(color: AppColors.golden),
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        ),
                        child: const Text(
                          'Buy Gold',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Silver balance card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                  decoration: BoxDecoration(
                    color: Colors.white, // White background
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: AppColors.darkEliteGreen), // Green border
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadowGreen.withOpacity(0.2),
                        blurRadius: 10,
                        spreadRadius: 0,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Silver amount with label
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: AppColors.silverPrimary.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Container(
                                  width: 18,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    color: AppColors.silverPrimary,
                                    borderRadius: BorderRadius.circular(3),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.3),
                                        blurRadius: 1,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'Available Silver',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.silverPrimary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.baseline,
                            textBaseline: TextBaseline.alphabetic,
                            children: [
                              Text(
                                _silverBalance.toStringAsFixed(3),
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textGreen,
                                ),
                              ),
                              const SizedBox(width: 4),
                              const Text(
                                'g',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.textGreen,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          // Weight in vori
                          Text(
                            '(${_gramsToVori(_silverBalance).toStringAsFixed(4)} vori)',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: AppColors.textMedium.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),

                      // Buy more button
                      ElevatedButton(
                        onPressed: () {
                          // Navigate to Improved Products screen
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const ImprovedProductsScreen(),
                            ),
                          ).then((_) {
                            // Refresh data when returning from purchase screen
                            if (mounted) {
                              _loadAssetsData();
                            }
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppColors.silverPrimary,
                          side: const BorderSide(color: AppColors.silverPrimary),
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        ),
                        child: const Text(
                          'Buy Silver',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 15),

                // Asset details section title
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        // Vertical bar
                        Container(
                          width: 3,
                          height: 18,
                          decoration: BoxDecoration(
                            color: AppColors.darkEliteGreen,
                            borderRadius: BorderRadius.circular(1.5),
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Title
                        const Text(
                          'DELIVERY STATUS',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textGreen, // Green text
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 10),

                // Asset details section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white, // White background
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: AppColors.darkEliteGreen), // Green border
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadowGreen.withOpacity(0.2),
                        blurRadius: 8,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Gold details
                      Row(
                        children: [
                          // Gold icon
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppColors.golden.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Container(
                              width: 24,
                              height: 16,
                              decoration: BoxDecoration(
                                color: AppColors.golden,
                                borderRadius: BorderRadius.circular(4),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 1,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),

                          // Gold details
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'GOLD',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.golden,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    _buildAssetDetail('Delivered', _deliveredGold),
                                    const SizedBox(width: 12),
                                    _buildAssetDetail('In Progress', _inProgressGold),
                                    const SizedBox(width: 12),
                                    _buildAssetDetail('Total', _totalPurchasedGold),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),
                      const Divider(color: AppColors.cardBorderSubtle),
                      const SizedBox(height: 8),

                      // Silver details
                      Row(
                        children: [
                          // Silver icon
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppColors.silverPrimary.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Container(
                              width: 24,
                              height: 16,
                              decoration: BoxDecoration(
                                color: AppColors.silverPrimary,
                                borderRadius: BorderRadius.circular(4),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 1,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),

                          // Silver details
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'SILVER',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.silverPrimary,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    _buildAssetDetail('Delivered', _deliveredSilver, isGold: false),
                                    const SizedBox(width: 16),
                                    _buildAssetDetail('In Progress', _inProgressSilver, isGold: false),
                                    const SizedBox(width: 16),
                                    _buildAssetDetail('Total', _totalPurchasedSilver, isGold: false),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 15),

                // Action Buttons
                Row(
                  children: [
                    // Request Delivery Button
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          if (_goldBalance > 0 || _silverBalance > 0) {
                            // Check KYC status before navigating
                            _checkAndNavigateToDelivery();
                          } else {
                            // Show dialog when no assets are available
                            _showNoAssetsDialog();
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(10), // 10% smaller padding
                          decoration: BoxDecoration(
                            color: Colors.white, // White background
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: AppColors.darkEliteGreen), // Green border
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.shadowGreen.withOpacity(0.2),
                                blurRadius: 10,
                                spreadRadius: 0,
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.local_shipping_outlined,
                                color: (_goldBalance > 0 || _silverBalance > 0)
                                    ? AppColors.textGreen
                                    : AppColors.textGreen.withOpacity(0.5),
                                size: 28, // 10% smaller icon
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Request Delivery',
                                style: TextStyle(
                                  color: (_goldBalance > 0 || _silverBalance > 0)
                                      ? AppColors.textGreen
                                      : AppColors.textGreen.withOpacity(0.5),
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // View Deliveries Button
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const DeliveryTab(initialTabIndex: 0),
                            ),
                          ).then((_) {
                            if (mounted) {
                              _loadAssetsData();
                            }
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.all(10), // 10% smaller padding
                          decoration: BoxDecoration(
                            color: Colors.white, // White background
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: AppColors.darkEliteGreen), // Green border
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.shadowGreen.withOpacity(0.2),
                                blurRadius: 10,
                                spreadRadius: 0,
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.visibility,
                                color: AppColors.textGreen,
                                size: 28, // 10% smaller icon
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'View Deliveries',
                                style: TextStyle(
                                  color: AppColors.textGreen,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                // Pending Deliveries Indicator
                if (_inProgressGold > 0 || _inProgressSilver > 0)
                  Container(
                    margin: const EdgeInsets.only(top: 16),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.warning),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.warning.withOpacity(0.1),
                          blurRadius: 8,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.info_outline,
                          color: AppColors.warning,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'You have ${_deliveryRequests.where((d) => d.status != 'delivered' && d.status != 'cancelled').length} active delivery requests in progress.',
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.warning,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
        ),
      );
    }

    return Scaffold(
      appBar: const BrandedAppBar(
        title: 'My Assets',
      ),
      body: content,
    );
  }

  // Helper method to build asset detail widgets
  Widget _buildAssetDetail(String label, double value, {bool isGold = true}) {
    final color = isGold ? AppColors.golden : AppColors.silverPrimary;
    final voriValue = _gramsToVori(value);

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              color: color.withOpacity(0.85),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                value.toStringAsFixed(2),
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                  color: Colors.black, // Changed to black for better readability
                ),
              ),
              const SizedBox(width: 2),
              const Text(
                'g',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.black, // Changed to black for better readability
                ),
              ),
            ],
          ),
          // Weight in vori
          Text(
            '(${voriValue.toStringAsFixed(4)} vori)',
            style: const TextStyle(
              fontSize: 9,
              color: Colors.black, // Changed to black for better readability
            ),
          ),
        ],
      ),
    );
  }

  // Check KYC status and navigate to appropriate screen
  void _checkAndNavigateToDelivery() async {
    // Refresh KYC status and user data first
    await _checkKycStatus();
    await _fetchCurrentUser();

    // Check if user profile has complete address information
    if (!_isAddressComplete()) {
      // Show a message that the user needs to update their profile
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please update your profile with address and city information before requesting delivery.'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );

      // Navigate to the Account tab where they can update their profile
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(
          builder: (context) => const DashboardScreen(initialTabIndex: 2), // Account tab
        ),
        (route) => false,
      );
      return;
    }

    // If address is complete, proceed to delivery request screen
    // KYC verification is recommended but not required
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DeliveryRequestScreen(
          availableGold: _goldBalance,
          availableSilver: _silverBalance,
          isInTabView: false,
        ),
      ),
    ).then((_) {
      if (mounted) {
        _loadAssetsData();
      }
    });
  }

  // Show dialog when user has no available assets
  void _showNoAssetsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.warning,
                size: 24,
              ),
              SizedBox(width: 10),
              Text(
                'No Available Assets',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textGreen,
                ),
              ),
            ],
          ),
          content: Text(
            'You can place a delivery request when you have available Gold or Silver.',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textDark,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'OK',
                style: TextStyle(
                  color: AppColors.textGreen,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
