import 'package:flutter/material.dart';
import 'package:goldencyz_app/constants.dart';
import 'package:goldencyz_app/models/gold_rate.dart';
import 'package:goldencyz_app/models/silver_rate.dart';
import 'package:goldencyz_app/widgets/metal_rates_card.dart';
import 'package:goldencyz_app/widgets/animated_border_card.dart';
import 'package:goldencyz_app/widgets/animated_gold_green_button.dart';
import 'package:goldencyz_app/screens/gold/gold_purchase_screen.dart';
import 'package:goldencyz_app/screens/silver/silver_purchase_screen.dart';
import 'package:goldencyz_app/screens/products/improved_products_screen.dart';
import 'package:goldencyz_app/screens/gold/gold_rate_history_screen.dart';
import 'package:goldencyz_app/screens/gift/send_gift_screen.dart';
import 'package:goldencyz_app/screens/invoice/invoice_list_screen.dart';
import 'package:goldencyz_app/screens/delivery/delivery_request_screen.dart';
import 'package:goldencyz_app/screens/delivery/delivery_tab.dart';
import 'package:goldencyz_app/screens/order/orders_screen.dart';
import 'package:goldencyz_app/screens/notifications/notification_screen.dart';
import 'package:goldencyz_app/services/service_provider.dart';
import 'package:goldencyz_app/widgets/gold_price_card.dart';
import 'package:goldencyz_app/widgets/gold_rate_chart.dart';
import 'package:goldencyz_app/widgets/notification_icon.dart';
import 'package:goldencyz_app/widgets/branded_app_bar.dart';
import 'package:goldencyz_app/widgets/loading_indicator.dart';
import 'package:goldencyz_app/widgets/article_slider.dart';
import 'package:badges/badges.dart' as badges;
import 'package:intl/intl.dart';

class HomeTab extends StatefulWidget {
  final GoldRate? latestRate;
  final bool isLoading;
  final Future<void> Function()? onRefreshGoldRate;

  const HomeTab({
    super.key,
    required this.latestRate,
    required this.isLoading,
    this.onRefreshGoldRate,
  });

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> {
  final ServiceProvider _serviceProvider = ServiceProvider();
  SilverRate? _silverRate;
  bool _isLoadingSilver = true;

  @override
  void initState() {
    super.initState();
    _loadSilverRate();
  }

  Future<void> _loadSilverRate() async {
    try {
      final silverRate = await _serviceProvider.silverRateService.getLatestRate();
      if (mounted) {
        setState(() {
          _silverRate = silverRate;
          _isLoadingSilver = false;
        });
      }
    } catch (e) {
      print('Error loading silver rate: $e');
      if (mounted) {
        setState(() {
          _isLoadingSilver = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const BrandedAppBar(
        title: 'GoldenCYZ',
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.backgroundGradientStart, // Gold gradient start
              AppColors.backgroundGradientEnd, // White gradient end
            ],
          ),
        ),
        child: RefreshIndicator(
          onRefresh: () async {
            // Create a new service provider instance to refresh data
            final serviceProvider = ServiceProvider();

            // Refresh gold rate if parent provided a callback
            if (widget.onRefreshGoldRate != null) {
              await widget.onRefreshGoldRate!();
            }

            if (mounted) {
              // Refresh silver rate
              setState(() {
                _isLoadingSilver = true;
              });

              try {
                final silverRate = await serviceProvider.silverRateService.getLatestRate();
                if (mounted) {
                  setState(() {
                    _silverRate = silverRate;
                    _isLoadingSilver = false;
                  });
                }
              } catch (e) {
                print('Error refreshing silver rate: $e');
                if (mounted) {
                  setState(() {
                    _isLoadingSilver = false;
                  });
                }
              }
            }
          },
          color: AppColors.textGreen, // Green color for refresh indicator
          backgroundColor: Colors.white, // White background
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Metal rates card
              MetalRatesCard(
                goldRate: widget.latestRate,
                silverRate: _silverRate,
                isLoading: widget.isLoading || _isLoadingSilver,
              ),

              const SizedBox(height:32),
              // Animated Buy Gold & Silver button with green and gold colors
              AnimatedGoldGreenButton(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ImprovedProductsScreen(),
                    ),
                  );
                },
                child: Row(
                  children: const [
                    Icon(
                      Icons.shopping_bag_outlined,
                      color: Colors.black, // Black icon
                      size: 32,
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Buy Gold & Silver',
                            style: TextStyle(
                              color: Colors.black, // Black text
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            'Purity Assured',
                            style: TextStyle(
                              color: Color(0xFF333333), // Dark gray text
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.black, // Black icon
                      size: 16,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildActionCard(
                      context,
                      'Request Delivery',
                      Icons.local_shipping_outlined,
                      () {
                        // Navigate directly to the Request Delivery tab
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const DeliveryTab(initialTabIndex: 2),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildActionCard(
                      context,
                      'View Orders',
                      Icons.shopping_bag_outlined,
                      () {
                        // Navigate to the Orders screen
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const OrdersScreen(),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Row removed - View Invoices moved to Account -> My Transactions

              // Article slider
              const ArticleSlider(
                height: 180,
              ),

              const SizedBox(height: 5),
              // Gold price trend
              const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Gold Price Trend',
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.normal,
                      color: AppColors.textGreen, // Green text
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              // Gold rate chart for 7 days
              const GoldRateChart(
                days: 14,
                height: 200,
                showTitle: false, // We already have a title above
              ),
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
    {Color color = AppColors.textGreen, bool isWide = false}
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        width: isWide ? double.infinity : null,
        decoration: BoxDecoration(
          color: Colors.white, // White background
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.darkEliteGreen), // Green border
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowGreen,
              blurRadius: 10,
              spreadRadius: 0,
            ),
          ],
        ),
        child: isWide
            ? Row(
                children: [
                  Icon(
                    icon,
                    color: color,
                    size: 32,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            color: color,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Shop our combined products',
                          style: TextStyle(
                            color: AppColors.textMedium, // Dark gray text
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: color,
                    size: 16,
                  ),
                ],
              )
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    icon,
                    color: color,
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    title,
                    style: const TextStyle(
                      color: AppColors.textGreen, // Green text
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
      ),
    );
  }
}
