import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:goldencyz_app/constants.dart';
import 'package:goldencyz_app/models/user.dart';
import 'package:goldencyz_app/screens/auth/phone_input_screen.dart';
import 'package:goldencyz_app/screens/profile/profile_screen.dart';
import 'package:goldencyz_app/screens/dashboard/dashboard_screen.dart';
import 'package:goldencyz_app/screens/invoice/invoices_screen.dart';
import 'package:goldencyz_app/screens/settings/settings_screen.dart';
import 'package:goldencyz_app/screens/notifications/notification_screen.dart';
import 'package:goldencyz_app/screens/delivery/delivery_tab.dart';
import 'package:goldencyz_app/screens/products/improved_products_screen.dart';
import 'package:goldencyz_app/screens/kyc/kyc_verification_screen.dart';
import 'package:goldencyz_app/widgets/notification_icon.dart';
import 'package:goldencyz_app/widgets/branded_app_bar.dart';
import 'package:goldencyz_app/services/service_provider.dart';
import 'package:goldencyz_app/widgets/loading_indicator.dart';
import 'package:goldencyz_app/widgets/styled_card.dart';
import 'package:goldencyz_app/widgets/styled_button.dart';

class ProfileTab extends StatefulWidget {
  const ProfileTab({super.key});

  @override
  State<ProfileTab> createState() => _ProfileTabState();
}

class _ProfileTabState extends State<ProfileTab> {
  final ServiceProvider _serviceProvider = ServiceProvider();
  bool _isLoading = true;
  User? _user;
  Map<String, dynamic>? _kycStatus;

  @override
  void initState() {
    super.initState();
    _fetchUserProfile();
    _checkKycStatus();
  }

  Future<void> _fetchUserProfile() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Use the API service directly to get the user data
      final response = await _serviceProvider.apiService.get('/user');
      print('Profile tab loaded user directly: ${response['name']} (ID: ${response['id']})');

      if (mounted) {
        setState(() {
          _user = User.fromJson(response);
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching profile: $e');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error fetching profile: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _checkKycStatus() async {
    try {
      // Get the user ID (for debugging purposes)
      final userId = await _serviceProvider.apiService.getUserId();
      print('Current user ID: $userId');

      // Use the unified metal service to check KYC status
      final status = await _serviceProvider.activeUnifiedMetalService.checkKycStatus(userId: userId);
      print('KYC Status loaded: $status');

      if (mounted) {
        setState(() {
          _kycStatus = status;
        });
      }
    } catch (e) {
      print('Error checking KYC status: $e');
      // Set default values for KYC status
      if (mounted) {
        setState(() {
          _kycStatus = {
            'status': 'not_submitted',
            'message': 'KYC verification not submitted',
            'is_verified': false,
            'display_message': 'Please submit your identity verification to request gold delivery.',
          };
        });
      }
    }
  }

  Future<void> _navigateToKycScreen() async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const KycVerificationScreen(),
      ),
    );

    // Refresh KYC status when returning from KYC screen
    if (mounted) {
      _checkKycStatus();
    }
  }

  void _contactSupport() {
    final String supportEmail = '<EMAIL>';

    // Show a dialog with only copy email option
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Contact Support',
            style: TextStyle(
              color: AppColors.textGreen,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Contact our support team via email:',
                style: TextStyle(
                  color: AppColors.textDark,
                ),
              ),
              const SizedBox(height: 16),
              _buildContactOption(
                icon: Icons.content_copy_outlined,
                title: 'Copy Email Address',
                subtitle: supportEmail,
                onTap: () {
                  // Copy email to clipboard
                  Clipboard.setData(ClipboardData(text: supportEmail));
                  Navigator.pop(context);

                  // Show confirmation
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Email address copied to clipboard'),
                      backgroundColor: AppColors.textGreen,
                    ),
                  );
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text(
                'Close',
                style: TextStyle(
                  color: AppColors.textLight,
                ),
              ),
            ),
          ],
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        );
      },
    );
  }

  Widget _buildContactOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.golden.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: AppColors.golden,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: AppColors.textGreen,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: AppColors.textMedium,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  Future<void> _logout() async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          backgroundColor: AppColors.surfaceLight,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              LoadingIndicator(),
              SizedBox(height: 16),
              Text('Logging out...', style: TextStyle(color: AppColors.textLight)),
            ],
          ),
        );
      },
    );

    try {
      // Always use the real API service
      await _serviceProvider.authService.logout();

      if (mounted) {
        // Close the loading dialog
        Navigator.of(context).pop();

        // Navigate to login screen
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
            builder: (context) => const PhoneInputScreen(),
          ),
          (route) => false,
        );
      }
    } catch (e) {
      print('Error logging out: $e');

      if (mounted) {
        // Close the loading dialog
        Navigator.of(context).pop();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error logging out: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const BrandedAppBar(
        title: 'My Account',
        showLogo: true,
        showNotification: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.backgroundGradientStart, // Gold gradient start
              AppColors.backgroundGradientEnd, // White gradient end
            ],
          ),
        ),
        child: RefreshIndicator(
          onRefresh: () async {
            await _fetchUserProfile();
            await _checkKycStatus();
          },
          color: AppColors.textGreen,
          backgroundColor: Colors.white,
          child: _isLoading
              ? const Center(child: LoadingIndicator())
              : _user == null
                  ? Center(
                      child: StyledCard(
                        backgroundColor: Colors.white,
                        borderColor: Colors.red.withOpacity(0.5),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 64,
                              color: AppColors.golden.withOpacity(0.5),
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'Failed to load profile',
                              style: TextStyle(
                                color: AppColors.textDark,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            StyledButton(
                              text: 'Retry',
                              onPressed: _fetchUserProfile,
                              buttonColor: AppColors.textGreen,
                              textColor: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    )
                  : ListView(
                      padding: const EdgeInsets.all(16),
                      children: [
                        // Profile header
                        StyledCard(
                          backgroundColor: Colors.white,
                          borderColor: AppColors.golden.withOpacity(0.5),
                          borderRadius: 16,
                          child: Column(
                            children: [
                              // Profile picture
                              Container(
                                width: 100,
                                height: 100,
                                decoration: BoxDecoration(
                                  color: AppColors.golden.withOpacity(0.2),
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: AppColors.golden,
                                    width: 2,
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    _user!.name != null && _user!.name!.isNotEmpty
                                        ? _user!.name![0].toUpperCase()
                                        : 'U',
                                    style: const TextStyle(
                                      color: AppColors.golden,
                                      fontSize: 40,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),
                              // User name
                              Text(
                                _user!.name ?? 'User',
                                style: const TextStyle(
                                  color: AppColors.textGreen,
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              // Phone number
                              Text(
                                _user!.phoneNumber != null ? '${_user!.phoneNumber}' : '',
                                style: const TextStyle(
                                  color: AppColors.textDark,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 16),
                              // Edit profile button
                              StyledButton(
                                text: 'Edit Profile',
                                onPressed: () {
                                  // Navigate to edit profile screen
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const ProfileScreen(),
                                    ),
                                  );
                                },
                                isPrimary: false,
                                buttonColor: AppColors.textGreen,
                              ),
                              const SizedBox(height: 12),
                              // Logout button
                              TextButton.icon(
                                onPressed: _logout,
                                icon: const Icon(
                                  Icons.logout,
                                  size: 18,
                                  color: AppColors.textGreen,
                                ),
                                label: const Text(
                                  'Logout',
                                  style: TextStyle(
                                    color: AppColors.textGreen,
                                    fontSize: 14,
                                  ),
                                ),
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                        // Primary Functions section, disabled in version 1.0.0
                        //_buildIdentityVerificationCard(),

                        _buildMenuItem(
                          'My Assets',
                          'Your gold & silver balance and delivery history',
                          Icons.monetization_on_outlined,
                          () {
                            // Switch to the Gold Collection tab in the bottom navigation
                            Navigator.of(context).pushReplacement(
                              MaterialPageRoute(
                                builder: (context) => const DashboardScreen(initialTabIndex: 1),
                              ),
                            );
                          },
                        ),

                        _buildMenuItem(
                          'Shop Products',
                          'Browse and purchase gold and silver products',
                          Icons.shopping_bag_outlined,
                          () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const ImprovedProductsScreen(),
                              ),
                            );
                          },
                        ),

                        _buildMenuItem(
                          'My Transactions',
                          'View your order and payment history',
                          Icons.receipt_long_outlined,
                          () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const InvoicesScreen(),
                              ),
                            );
                          },
                        ),

                        _buildMenuItem(
                          'Deliveries',
                          'Track and manage your gold deliveries',
                          Icons.local_shipping_outlined,
                          () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const DeliveryTab(),
                              ),
                            );
                          },
                        ),

                        // Contact Support at the end
                        _buildContactSupportCard(),

                        const SizedBox(height: 24),
                        // App information
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.darkEliteGreen.withOpacity(0.15),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            children: [
                              const Text(
                                'App Information',
                                style: TextStyle(
                                  color: AppColors.golden,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'GoldenCYZ v1.0.0',
                                style: TextStyle(
                                  color: AppColors.textLight,
                                  fontSize: 14,
                                ),
                              ),
                              const SizedBox(height: 4),
                              const Text(
                                'A Product of Ruhama IT Solutions',
                                style: TextStyle(
                                  color: Colors.green,
                                  fontSize: 14,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 8, bottom: 8),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.textGreen,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textGreen,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.golden,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: AppColors.textLight.withOpacity(0.7),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    color: AppColors.textLight,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getKycStatusText(String status) {
    switch (status) {
      case 'verified':
        return 'Verified';
      case 'pending':
        return 'Pending';
      case 'rejected':
        return 'Rejected';
      case 'not_submitted':
        return 'Not Submitted';
      default:
        return 'Unknown';
    }
  }

  Color _getKycStatusColor(String status) {
    switch (status) {
      case 'verified':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'rejected':
        return Colors.red;
      case 'not_submitted':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _getKycStatusDescription(String status) {
    switch (status) {
      case 'verified':
        return 'Your identity has been verified successfully.';
      case 'pending':
        return 'Your verification is being reviewed by our team.';
      case 'rejected':
        return 'Your verification was rejected. Please submit again.';
      case 'not_submitted':
        return 'The identity verification is not submitted.';
      default:
        return 'Unknown status. Please contact support.';
    }
  }

  IconData _getKycStatusIcon(String status) {
    switch (status) {
      case 'verified':
        return Icons.verified_user;
      case 'pending':
        return Icons.hourglass_top;
      case 'rejected':
        return Icons.error_outline;
      case 'not_submitted':
        return Icons.person_outline;
      default:
        return Icons.help_outline;
    }
  }

  Widget _buildIdentityVerificationCard() {
    final status = _kycStatus?['status'] ?? 'not_submitted';
    final statusText = _getKycStatusText(status);
    final statusColor = _getKycStatusColor(status);
    final statusDescription = _getKycStatusDescription(status);
    final statusIcon = _getKycStatusIcon(status);

    return StyledCard(
      margin: const EdgeInsets.only(bottom: 12),
      backgroundColor: Colors.white,
      elevation: 4,
      borderColor: statusColor.withOpacity(0.5),
      borderRadius: 12,
      child: InkWell(
        onTap: _navigateToKycScreen,
        borderRadius: BorderRadius.circular(12),
        splashColor: AppColors.golden.withOpacity(0.1),
        highlightColor: AppColors.golden.withOpacity(0.05),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Icon with gradient background
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      statusColor.withOpacity(0.2),
                      statusColor.withOpacity(0.1),
                    ],
                  ),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: statusColor.withOpacity(0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: statusColor.withOpacity(0.3),
                      blurRadius: 4,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(statusIcon, color: statusColor, size: 24),
              ),
              const SizedBox(width: 16),
              // Title and subtitle
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Identity Verification',
                      style: TextStyle(
                        color: AppColors.textGreen,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: statusColor,
                          width: 0.5,
                        ),
                      ),
                      child: Text(
                        statusText,
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      statusDescription,
                      style: TextStyle(
                        color: AppColors.textMedium,
                        fontSize: 14,
                      ),
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to ${status == 'not_submitted' || status == 'rejected' ? 'submit' : 'view'} verification',
                      style: TextStyle(
                        color: statusColor,
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ],
                ),
              ),
              // Arrow icon
              const Icon(
                Icons.arrow_forward_ios,
                color: AppColors.textGreen,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContactSupportCard() {
    return StyledCard(
      margin: const EdgeInsets.only(bottom: 12),
      backgroundColor: Colors.white,
      elevation: 4,
      borderColor: AppColors.textGreen.withOpacity(0.5),
      borderRadius: 12,
      child: InkWell(
        onTap: _contactSupport,
        borderRadius: BorderRadius.circular(12),
        splashColor: AppColors.golden.withOpacity(0.1),
        highlightColor: AppColors.golden.withOpacity(0.05),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Icon with gradient background
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.textGreen.withOpacity(0.2),
                      AppColors.textGreen.withOpacity(0.1),
                    ],
                  ),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.textGreen.withOpacity(0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadowGreen.withOpacity(0.3),
                      blurRadius: 4,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(Icons.support_agent, color: AppColors.textGreen, size: 24),
              ),
              const SizedBox(width: 16),
              // Title and subtitle
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Contact Support',
                      style: TextStyle(
                        color: AppColors.textGreen,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'Get help with your account, orders, or deliveries',
                      style: TextStyle(
                        color: AppColors.textMedium,
                        fontSize: 14,
                      ),
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to contact our support team',
                      style: TextStyle(
                        color: AppColors.textGreen,
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
              // Arrow icon
              const Icon(
                Icons.arrow_forward_ios,
                color: AppColors.textGreen,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuItem(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return StyledCard(
      margin: const EdgeInsets.only(bottom: 12),
      backgroundColor: Colors.white,
      elevation: 4,
      borderColor: AppColors.cardBorder,
      borderRadius: 12,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        splashColor: AppColors.golden.withOpacity(0.1),
        highlightColor: AppColors.golden.withOpacity(0.05),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Icon with gradient background
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.textGreen.withOpacity(0.2),
                      AppColors.textGreen.withOpacity(0.1),
                    ],
                  ),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.textGreen.withOpacity(0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadowGreen.withOpacity(0.3),
                      blurRadius: 4,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(icon, color: AppColors.textGreen, size: 24),
              ),
              const SizedBox(width: 16),
              // Title and subtitle
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        color: AppColors.textGreen,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        color: AppColors.textMedium,
                        fontSize: 14,
                      ),
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
              // Arrow icon
              const Icon(
                Icons.arrow_forward_ios,
                color: AppColors.textGreen,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
