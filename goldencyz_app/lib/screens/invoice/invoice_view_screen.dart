import 'package:flutter/material.dart';
import 'package:goldencyz_app/config/api_config.dart';
import 'package:goldencyz_app/constants.dart';
import 'package:goldencyz_app/models/invoice.dart';
import 'package:goldencyz_app/services/service_provider.dart';
import 'package:goldencyz_app/utils/responsive_helper.dart';
import 'package:goldencyz_app/widgets/branded_app_bar.dart';
import 'package:goldencyz_app/widgets/loading_indicator.dart';
import 'package:goldencyz_app/widgets/watermark_overlay.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

// Extension to capitalize first letter of a string
extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${this.substring(1)}";
  }
}

class InvoiceViewScreen extends StatefulWidget {
  final int invoiceId;

  const InvoiceViewScreen({
    super.key,
    required this.invoiceId,
  });

  @override
  State<InvoiceViewScreen> createState() => _InvoiceViewScreenState();
}

class _InvoiceViewScreenState extends State<InvoiceViewScreen> {
  final ServiceProvider _serviceProvider = ServiceProvider.instance;
  bool _isLoading = true;
  InvoiceData? _invoiceData;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchInvoiceData();
  }

  Future<void> _fetchInvoiceData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final invoiceData = await _serviceProvider.invoiceService.getInvoiceData(widget.invoiceId);

      if (invoiceData == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load invoice data';
        });
        return;
      }

      // Log invoice data for debugging
      print('Invoice data loaded for ID: ${widget.invoiceId}');
      print('Invoice number: ${invoiceData.invoice.invoiceNumber}');
      print('Invoice type: ${invoiceData.invoice.invoiceType}');
      print('Gold amount: ${invoiceData.invoice.goldAmount}g');
      print('Silver amount: ${invoiceData.invoice.silverAmount}g');
      print('Purchase type: ${invoiceData.purchase.type}');
      print('Purchase gold amount: ${invoiceData.purchase.goldAmount}g');
      print('Purchase silver amount: ${invoiceData.purchase.silverAmount}g');

      // Log associated orders if available
      if (invoiceData.associatedOrders != null) {
        print('Associated orders: ${invoiceData.associatedOrders!.length}');
        if (invoiceData.associatedOrders!.isNotEmpty) {
          print('First order: ${invoiceData.associatedOrders![0]}');
        }
      } else {
        print('No associated orders found');
      }

      setState(() {
        _invoiceData = invoiceData;
        _isLoading = false;
      });
    } catch (e) {
      print('Error fetching invoice data: $e');
      print('Error type: ${e.runtimeType}');
      print('Full error details: ${e.toString()}');

      String userFriendlyMessage;

      // Check for specific error messages and provide user-friendly messages
      if (e.toString().contains('missing purchase information')) {
        print('Handling missing purchase information error');
        userFriendlyMessage = 'This invoice cannot be displayed because the purchase information is missing. The product may have been removed from the system.';
      } else if (e.toString().contains('404')) {
        print('Handling 404 error');
        userFriendlyMessage = 'Invoice not found. It may have been deleted or is no longer available.';
      } else if (e.toString().contains('Purchase information not found')) {
        print('Handling Purchase information not found error');
        userFriendlyMessage = 'This invoice cannot be displayed because the purchase information is missing. The product may have been removed from the system.';
      } else {
        print('Handling generic error');
        userFriendlyMessage = 'Error loading invoice: ${e.toString().split('Exception:').last.trim()}';
      }

      print('Setting error message: $userFriendlyMessage');

      setState(() {
        _isLoading = false;
        _errorMessage = userFriendlyMessage;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget content;

    if (_isLoading) {
      content = const Center(child: LoadingIndicator());
    } else if (_errorMessage != null) {
      content = Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.amber.withOpacity(0.7),
              ),
              const SizedBox(height: 20),
              Text(
                _errorMessage!,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.textDark,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton(
                    onPressed: _fetchInvoiceData,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.golden,
                      foregroundColor: AppColors.textGreen,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                        side: const BorderSide(color: AppColors.darkEliteGreen, width: 1),
                      ),
                      elevation: 0,
                    ),
                    child: const Text('Retry'),
                  ),
                  const SizedBox(width: 16),
                  OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.darkEliteGreen,
                      side: const BorderSide(color: AppColors.darkEliteGreen),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    } else {
      content = _buildInvoiceContent();
    }

    // Apply watermark overlay
    content = WatermarkOverlay(
      enabled: false, // Set to true to enable the watermark
      text: 'DEMO GOLD ONLY',
      child: content,
    );

    return Scaffold(
      appBar: const BrandedAppBar(
        title: 'Invoice',
        showLogo: true,
        showNotification: true,
      ),
      body: Container(
        child: content,
      ),
    );
  }

  // Helper method to check if this is a delivery invoice
  bool _isDeliveryInvoice() {
    if (_invoiceData == null) return false;

    // Check if invoice_type is explicitly set to 'delivery'
    if (_invoiceData!.invoice.invoiceType == 'delivery') return true;

    // Check invoice number format (DEL- prefix)
    if (_invoiceData!.invoice.invoiceNumber.startsWith('DEL-') ||
        _invoiceData!.invoice.invoiceNumber.startsWith('INV-DEL')) return true;

    return false;
  }

  Widget _buildInvoiceContent() {
    if (_invoiceData == null) {
      return const Center(child: Text('No invoice data available'));
    }

    final currencyFormat = NumberFormat.currency(
      locale: 'en_BD',
      symbol: '৳',
      decimalDigits: 2,
    );

    // Use LayoutBuilder to ensure we're adapting to the available space
    return LayoutBuilder(
      builder: (context, constraints) {
        // Get responsive padding
        final padding = ResponsiveHelper.getResponsivePadding(
          context,
          padding: ResponsiveHelper.edgeInsetsFrom(const EdgeInsets.all(16.0)),
          scaleFactor: 0.9, // Slightly reduce padding on smaller screens
        );

        return SingleChildScrollView(
          // Enable physics to ensure scrolling works properly
          physics: const AlwaysScrollableScrollPhysics(),
          child: Padding(
            padding: padding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInvoiceHeader(),
                const SizedBox(height: 16), // Reduced spacing
                _buildInvoiceDetails(), // New invoice details section
                const SizedBox(height: 16), // Reduced spacing
                _buildCustomerInfo(),
                const SizedBox(height: 16), // Reduced spacing

                // Show either delivery details or purchase details based on invoice type
                _isDeliveryInvoice()
                  ? _buildDeliveryDetails(currencyFormat)
                  : _buildPurchaseDetails(currencyFormat),

                const SizedBox(height: 16), // Reduced spacing
                _buildPaymentSummary(currencyFormat),
                const SizedBox(height: 20), // Reduced spacing
                _buildInvoiceFooter(),
                const SizedBox(height: 20),
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // Generate a print-ready version of the invoice
                      _printInvoice();
                    },
                    icon: const Icon(Icons.download, size: 18),
                    label: const Text('Download Invoice'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.golden,
                      foregroundColor: AppColors.textGreen,
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                        side: const BorderSide(color: AppColors.darkEliteGreen, width: 1),
                      ),
                      elevation: 0,
                    ),
                  ),
                ),
                // Add extra space at the bottom to ensure the download button is visible
                const SizedBox(height: 20),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInvoiceHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (_invoiceData!.company.vatCountry != null && _invoiceData!.company.vatCountry!.isNotEmpty) ...[
            Text(
              _invoiceData!.company.vatCountry!,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
          if (_invoiceData!.company.govtOrg != null && _invoiceData!.company.govtOrg!.isNotEmpty) ...[
            Text(
              _invoiceData!.company.govtOrg!,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
          if (_invoiceData!.company.mushak != null && _invoiceData!.company.mushak!.isNotEmpty) ...[
            Text(
              _invoiceData!.company.mushak!,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
          if ((_invoiceData!.company.bin != null && _invoiceData!.company.bin!.isNotEmpty) ||
              (_invoiceData!.company.tin != null && _invoiceData!.company.tin!.isNotEmpty)) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_invoiceData!.company.bin != null && _invoiceData!.company.bin!.isNotEmpty) ...[
                  Text('BIN: ${_invoiceData!.company.bin!}'),
                ],
                if (_invoiceData!.company.bin != null && _invoiceData!.company.bin!.isNotEmpty &&
                    _invoiceData!.company.tin != null && _invoiceData!.company.tin!.isNotEmpty) ...[
                  Text(' | '),
                ],
                if (_invoiceData!.company.tin != null && _invoiceData!.company.tin!.isNotEmpty) ...[
                  Text('Tax ID: ${_invoiceData!.company.tin!}'),
                ],
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCustomerInfo() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.darkEliteGreen, width: 1),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowGreen.withOpacity(0.1),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Row(
              children: [
                Container(
                  width: 3,
                  height: 16,
                  decoration: BoxDecoration(
                    color: AppColors.darkEliteGreen,
                    borderRadius: BorderRadius.circular(1.5),
                  ),
                ),
                const SizedBox(width: 6),
                const Text(
                  'BILLED TO',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.darkEliteGreen,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              _invoiceData!.customer.name,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textGreen,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Address: ${_invoiceData!.customer.address} ${_invoiceData!.customer.city}',
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
              ),
            ),
            if (_invoiceData!.customer.phone.isNotEmpty)
              Text(
                'Phone: ${_invoiceData!.customer.phone}',
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.textDark,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceDetails() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.darkEliteGreen, width: 1),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowGreen.withOpacity(0.1),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Row(
              children: [
                Container(
                  width: 3,
                  height: 16,
                  decoration: BoxDecoration(
                    color: AppColors.darkEliteGreen,
                    borderRadius: BorderRadius.circular(1.5),
                  ),
                ),
                const SizedBox(width: 6),
                const Text(
                  'INVOICE DETAILS',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.darkEliteGreen,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDetailRow('Invoice Number', _invoiceData!.invoice.invoiceNumber),
            _buildDetailRow('Date', _invoiceData!.invoice.date),
            _buildDetailRow(
              'Payment Status',
              _invoiceData!.invoice.metadata?['payment_status'] ?? 'Pending',
              valueColor: (_invoiceData!.invoice.metadata?['payment_status'] == 'Done') ?
                AppColors.success :
                (_invoiceData!.invoice.metadata?['payment_status'] == 'Failed' ?
                  AppColors.error :
                  AppColors.warning),
            ),
            if (_invoiceData!.invoice.fiscalYear != null && _invoiceData!.invoice.fiscalYear!.isNotEmpty)
              _buildDetailRow('Fiscal Year', _invoiceData!.invoice.fiscalYear!),
            if (_invoiceData!.invoice.mushakNumber != null && _invoiceData!.invoice.mushakNumber!.isNotEmpty)
              _buildDetailRow('Mushak Number', _invoiceData!.invoice.mushakNumber!),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryDetails(NumberFormat currencyFormat) {
    // Extract delivery info from the invoice data
    Map<String, dynamic> deliveryInfo = {};
    if (_invoiceData!.invoice.notes != null && _invoiceData!.invoice.notes!.isNotEmpty) {
      deliveryInfo['message'] = _invoiceData!.invoice.notes;
    }

    // Check if there's delivery_info in the response
    if (_invoiceData!.deliveryInfo != null) {
      deliveryInfo = _invoiceData!.deliveryInfo!;
    }

    // Debug log to check if associated orders are available
    print('Building delivery details with associated orders: ${_invoiceData!.associatedOrders?.length ?? 0}');
    if (_invoiceData!.associatedOrders != null) {
      print('First order: ${_invoiceData!.associatedOrders!.isNotEmpty ? _invoiceData!.associatedOrders![0] : "None"}');
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.darkEliteGreen, width: 1),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowGreen.withOpacity(0.1),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Row(
              children: [
                Container(
                  width: 3,
                  height: 16,
                  decoration: BoxDecoration(
                    color: AppColors.darkEliteGreen,
                    borderRadius: BorderRadius.circular(1.5),
                  ),
                ),
                const SizedBox(width: 6),
                const Text(
                  'DELIVERY DETAILS',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.darkEliteGreen,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Delivery type banner - styled consistently with other banners
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.golden.withOpacity(0.15),
                    AppColors.goldenLight.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: AppColors.darkEliteGreen, width: 1),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowGreen.withOpacity(0.1),
                    blurRadius: 2,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: AppColors.darkEliteGreen, width: 1),
                    ),
                    child: const Icon(
                      Icons.local_shipping_outlined,
                      color: AppColors.darkEliteGreen,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'This is a delivery fee invoice',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.textGreen,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Delivery information
            if (deliveryInfo.containsKey('message') && deliveryInfo['message'] != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Delivery Information:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      deliveryInfo['message'],
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),

            // Metals being delivered - styled consistently with product displays
            if (_invoiceData!.invoice.goldAmount > 0 || _invoiceData!.invoice.silverAmount > 0) ...[
              const Padding(
                padding: EdgeInsets.only(bottom: 12.0),
                child: Text(
                  'Metals for Delivery:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: AppColors.textGreen,
                  ),
                ),
              ),

              // Enhanced metal display cards
              if (_invoiceData!.invoice.goldAmount > 0)
                Container(
                  margin: const EdgeInsets.only(bottom: 10),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.golden.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.golden.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Custom metal bar icon with improved styling
                      Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: AppColors.golden, width: 1),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.golden.withOpacity(0.2),
                              blurRadius: 2,
                              spreadRadius: 0,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Container(
                            width: 20,
                            height: 12,
                            decoration: BoxDecoration(
                              color: AppColors.golden,
                              borderRadius: BorderRadius.circular(2),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.3),
                                  blurRadius: 1,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Gold',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textGreen,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Amount: ${_invoiceData!.invoice.goldAmount.toStringAsFixed(3)} g',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppColors.textDark,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

              if (_invoiceData!.invoice.silverAmount > 0)
                Container(
                  margin: const EdgeInsets.only(bottom: 10),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.silverPrimary.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.silverPrimary.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Custom metal bar icon with improved styling
                      Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: AppColors.silverPrimary, width: 1),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.silverPrimary.withOpacity(0.2),
                              blurRadius: 2,
                              spreadRadius: 0,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Container(
                            width: 20,
                            height: 12,
                            decoration: BoxDecoration(
                              color: AppColors.silverPrimary,
                              borderRadius: BorderRadius.circular(2),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.3),
                                  blurRadius: 1,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Silver',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textGreen,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Amount: ${_invoiceData!.invoice.silverAmount.toStringAsFixed(3)} g',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppColors.textDark,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
            ],

            const SizedBox(height: 16),

            // Associated Orders section (if available)
            if (_invoiceData!.associatedOrders != null && _invoiceData!.associatedOrders!.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'Associated Orders:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),

              // Orders table - styled consistently with order invoice tables
              Table(
                columnWidths: const {
                  0: FlexColumnWidth(3.0), // Order Number & Date
                  1: FlexColumnWidth(1.5), // Status
                  2: FlexColumnWidth(1.2), // Gold
                  3: FlexColumnWidth(1.2), // Silver
                  4: FlexColumnWidth(1.8), // Amount
                },
                border: TableBorder.all(
                  color: Colors.grey.shade300,
                  width: 1,
                ),
                children: [
                  TableRow(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                    ),
                    children: const [
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'ORDER # / DATE',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'PAYMENT STATUS',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'GOLD (g)',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'SILVER (g)',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'AMOUNT',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                  ..._invoiceData!.associatedOrders!.map((order) => TableRow(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              order['order_number'] ?? 'N/A',
                              style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 12),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              order['created_at'] ?? 'N/A',
                              style: const TextStyle(fontSize: 11, color: Colors.grey),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: order.containsKey('payment_status') && order['payment_status'] == 'Done'
                                ? AppColors.success.withOpacity(0.2)
                                : order.containsKey('payment_status') && order['payment_status'] == 'Pending'
                                    ? AppColors.warning.withOpacity(0.2)
                                    : AppColors.error.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            order.containsKey('payment_status') && order['payment_status'] != null
                                ? order['payment_status'] as String
                                : 'Pending',
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                              color: order.containsKey('payment_status') && order['payment_status'] == 'Done'
                                  ? AppColors.success
                                  : order.containsKey('payment_status') && order['payment_status'] == 'Pending'
                                      ? AppColors.warning
                                      : AppColors.error,
                            ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          order.containsKey('total_gold_weight') && order['total_gold_weight'] != null &&
                          double.parse(order['total_gold_weight'].toString()) > 0
                              ? double.parse(order['total_gold_weight'].toString()).toStringAsFixed(2)
                              : '-',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          order.containsKey('total_silver_weight') && order['total_silver_weight'] != null &&
                          double.parse(order['total_silver_weight'].toString()) > 0
                              ? double.parse(order['total_silver_weight'].toString()).toStringAsFixed(2)
                              : '-',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          currencyFormat.format(double.parse((order['total_amount'] ?? 0).toString())),
                          style: const TextStyle(fontSize: 12),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  )).toList(),
                ],
              ),

              // Product details section
              const SizedBox(height: 16),
              const Text(
                'Product Details:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),

              // Products table - styled consistently with order invoice tables
              Table(
                columnWidths: const {
                  0: FlexColumnWidth(5), // Product with Order # and Type
                  1: FlexColumnWidth(1), // Weight
                  2: FlexColumnWidth(1), // Qty
                  3: FlexColumnWidth(2), // Subtotal
                },
                border: TableBorder.all(
                  color: Colors.grey.shade300,
                  width: 1,
                ),
                children: [
                  TableRow(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                    ),
                    children: const [
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'PRODUCT',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'WEIGHT',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'QTY',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'SUBTOTAL',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                  ..._invoiceData!.associatedOrders!.expand((order) {
                    final items = order.containsKey('items') ? (order['items'] as List<dynamic>? ?? []) : [];
                    return items.map((item) => TableRow(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                item['product_name'] ?? 'N/A',
                                style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 12),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                item['product_type'] != null ? (item['product_type'] as String).capitalize() : 'N/A',
                                style: const TextStyle(fontSize: 11, color: Colors.grey),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Order: ${order['order_number'] ?? 'N/A'}',
                                style: const TextStyle(fontSize: 11, color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            item['weight'] != null ? '${double.parse(item['weight'].toString()).toStringAsFixed(2)}g' : 'N/A',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            item['quantity']?.toString() ?? '1',
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            currencyFormat.format(double.parse((item['subtotal'] ?? 0).toString())),
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ));
                  }).toList(),
                ],
              ),
            ],

            const SizedBox(height: 16),

            // Delivery fee table - styled consistently with other tables
            Table(
              columnWidths: const {
                0: FlexColumnWidth(3),
                1: FlexColumnWidth(2),
              },
              border: TableBorder.all(
                color: Colors.grey.shade300,
                width: 1,
              ),
              children: [
                TableRow(
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                  ),
                  children: const [
                    Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Text(
                        'SERVICE',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Text(
                        'AMOUNT',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                TableRow(
                  children: [
                    const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Text(
                        'Delivery Service Fee',
                        style: TextStyle(fontWeight: FontWeight.w500, fontSize: 12),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        currencyFormat.format(_invoiceData!.invoice.amount),
                        style: const TextStyle(fontSize: 12),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPurchaseDetails(NumberFormat currencyFormat) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.darkEliteGreen, width: 1),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowGreen.withOpacity(0.1),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Row(
              children: [
                Container(
                  width: 3,
                  height: 16,
                  decoration: BoxDecoration(
                    color: AppColors.darkEliteGreen,
                    borderRadius: BorderRadius.circular(1.5),
                  ),
                ),
                const SizedBox(width: 6),
                const Text(
                  'PURCHASE DETAILS',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.darkEliteGreen,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // First check if we have items in the purchase object
            if (_invoiceData!.purchase.items != null && _invoiceData!.purchase.items!.isNotEmpty) ...[
              // Product details table
              Table(
                columnWidths: const {
                  0: FlexColumnWidth(2.5), // Product Name
                  1: FlexColumnWidth(1), // Quantity
                  2: FlexColumnWidth(2), // Weight
                  3: FlexColumnWidth(2), // Unit Price
                  4: FlexColumnWidth(2.5), // Total
                },
                border: TableBorder.all(
                  color: Colors.grey.shade300,
                  width: 1,
                ),
                children: [
                  // Table header
                  TableRow(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                    ),
                    children: const [
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'PRODUCT',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'QTY',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'WEIGHT',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'UNIT PRICE',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'TOTAL',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),

                  // Table rows for each product from purchase items
                  ..._invoiceData!.purchase.items!.map((item) => TableRow(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          item['product_name'] ?? 'N/A',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          item['quantity']?.toString() ?? '1',
                          style: const TextStyle(fontSize: 12),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          item['weight'] != null ? '${double.parse(item['weight'].toString()).toStringAsFixed(2)}g' : 'N/A',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          item['price_per_gram'] != null ? currencyFormat.format(double.parse(item['price_per_gram'].toString())) : 'N/A',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          item['subtotal'] != null ? currencyFormat.format(double.parse(item['subtotal'].toString())) : 'N/A',
                          style: const TextStyle(fontSize: 12),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  )).toList(),
                ],
              ),
            ]
            // Then check if we have items directly in the invoice data
            else if (_invoiceData!.invoice.items != null && _invoiceData!.invoice.items!.isNotEmpty) ...[
              // Product details table
              Table(
                columnWidths: const {
                  0: FlexColumnWidth(2.5), // Product Name
                  1: FlexColumnWidth(1), // Quantity
                  2: FlexColumnWidth(2), // Weight
                  3: FlexColumnWidth(2), // Unit Price
                  4: FlexColumnWidth(2.5), // Total

                },
                border: TableBorder.all(
                  color: Colors.grey.shade300,
                  width: 1,
                ),
                children: [
                  // Table header
                  TableRow(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                    ),
                    children: const [
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'PRODUCT',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'QTY',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'WEIGHT',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'UNIT PRICE',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'TOTAL',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),

                  // Table rows for each product from invoice items
                  ..._invoiceData!.invoice.items!.map((item) => TableRow(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          item['product_name'] ?? 'N/A',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          item['product_type'] != null ? (item['product_type'] as String).capitalize() : 'N/A',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          item['quantity']?.toString() ?? '1',
                          style: const TextStyle(fontSize: 12),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          item['weight'] != null ? '${double.parse(item['weight'].toString()).toStringAsFixed(2)}g' : 'N/A',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          item['price_per_gram'] != null ? currencyFormat.format(double.parse(item['price_per_gram'].toString())) : 'N/A',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          item['subtotal'] != null ? currencyFormat.format(double.parse(item['subtotal'].toString())) : 'N/A',
                          style: const TextStyle(fontSize: 12),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  )).toList(),
                ],
              ),
            ]
            // If no items in invoice or purchase, check associated orders
            else if (_invoiceData!.associatedOrders != null && _invoiceData!.associatedOrders!.isNotEmpty) ...[
              // Product details table
              Table(
                columnWidths: const {
                  0: FlexColumnWidth(3), // Product Name
                  1: FlexColumnWidth(1.5), // Type
                  2: FlexColumnWidth(1), // Quantity
                  3: FlexColumnWidth(1.5), // Weight
                  4: FlexColumnWidth(2), // Unit Price
                  5: FlexColumnWidth(2), // Total
                },
                border: TableBorder.all(
                  color: Colors.grey.shade300,
                  width: 1,
                ),
                children: [
                  // Table header
                  TableRow(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                    ),
                    children: const [
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'PRODUCT',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'TYPE',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'QTY',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'WEIGHT',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'UNIT PRICE',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'TOTAL',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),

                  // Table rows for each product from associated orders
                  ..._invoiceData!.associatedOrders!.expand((order) {
                    final items = order.containsKey('items') ? (order['items'] as List<dynamic>? ?? []) : [];
                    return items.map((item) => TableRow(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            item['product_name'] ?? 'N/A',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            item['product_type'] != null ? (item['product_type'] as String).capitalize() : 'N/A',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            item['quantity']?.toString() ?? '1',
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            item['weight'] != null ? '${double.parse(item['weight'].toString()).toStringAsFixed(2)}g' : 'N/A',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            item['price_per_gram'] != null ? currencyFormat.format(double.parse(item['price_per_gram'].toString())) : 'N/A',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            item['subtotal'] != null ? currencyFormat.format(double.parse(item['subtotal'].toString())) : 'N/A',
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ));
                  }).toList(),
                ],
              ),
            ] else ... [
              // If we have purchase items but they weren't displayed earlier, show them now
              if (_invoiceData!.purchase.items != null && _invoiceData!.purchase.items!.isNotEmpty)
                Column(
                  children: _invoiceData!.purchase.items!.map((item) {
                    final String productType = item['product_type'] ?? '';
                    final double weight = double.parse((item['weight'] ?? '0').toString());
                    final int quantity = int.parse((item['quantity'] ?? '1').toString());
                    final double pricePerGram = double.parse((item['price_per_gram'] ?? '0').toString());
                    final double subtotal = double.parse((item['subtotal'] ?? '0').toString());

                    return _buildPurchaseItemCard(
                      productType.capitalize(),
                      weight,
                      pricePerGram,
                      subtotal,
                      currencyFormat,
                      isGold: productType.toLowerCase() == 'gold',
                      productName: item['product_name'],
                      quantity: quantity,
                    );
                  }).toList(),
                )
              // If no purchase items, fall back to simplified display
              else
                Column(
                  children: [
                    if (_invoiceData!.purchase.type == 'gold' || _invoiceData!.invoice.goldAmount > 0)
                      _buildPurchaseItemCard(
                        'Gold',
                        _invoiceData!.invoice.goldAmount > 0 ? _invoiceData!.invoice.goldAmount : _invoiceData!.purchase.goldAmount,
                        _calculatePricePerGram('gold'),
                        _invoiceData!.invoice.amount,
                        currencyFormat,
                        isGold: true,
                      ),

                    if (_invoiceData!.purchase.type == 'silver' || _invoiceData!.invoice.silverAmount > 0 || _invoiceData!.purchase.silverAmount > 0)
                      _buildPurchaseItemCard(
                        'Silver',
                        _invoiceData!.invoice.silverAmount > 0 ? _invoiceData!.invoice.silverAmount : _invoiceData!.purchase.silverAmount,
                        _calculatePricePerGram('silver'),
                        _invoiceData!.invoice.amount,
                        currencyFormat,
                        isGold: false,
                      ),
                  ],
                )
            ],
          ],
        ),
      ),
    );
  }

  // Calculate price per gram based on invoice data
  double _calculatePricePerGram(String type) {
    double amount = _invoiceData!.invoice.amount;
    double weight = 0;

    if (type == 'gold') {
      weight = _invoiceData!.invoice.goldAmount > 0
          ? _invoiceData!.invoice.goldAmount
          : _invoiceData!.purchase.goldAmount;
    } else {
      weight = _invoiceData!.invoice.silverAmount > 0
          ? _invoiceData!.invoice.silverAmount
          : _invoiceData!.purchase.silverAmount;
    }

    // Avoid division by zero
    if (weight <= 0) return 0;

    return amount / weight;
  }

  Widget _buildPurchaseItemCard(String type, double weight, double pricePerGram, double total, NumberFormat currencyFormat, {
    bool isGold = true,
    String? productName,
    int quantity = 1
  }) {
    final Color itemColor = isGold ? AppColors.golden : AppColors.silverPrimary;
    final String displayProductName = productName ?? (isGold
        ? (weight <= 1 ? '1g Gold Bar' : '10g Gold Bar')
        : (weight <= 100 ? '100g Silver Bar' : '500g Silver Bar'));

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: itemColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: itemColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Custom metal bar icon with improved styling
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: itemColor, width: 1),
              boxShadow: [
                BoxShadow(
                  color: itemColor.withOpacity(0.2),
                  blurRadius: 4,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Center(
              child: Container(
                width: 28,
                height: 18,
                decoration: BoxDecoration(
                  color: itemColor,
                  borderRadius: BorderRadius.circular(3),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  displayProductName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textGreen,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Type: ${type.capitalize()} (${_invoiceData!.purchase.isGift ? 'Gift' : 'Purchase'})',
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textDark,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Weight: ${weight.toStringAsFixed(3)}g',
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textDark,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Price: ${currencyFormat.format(pricePerGram)}/g',
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textMuted,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                currencyFormat.format(total),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textGreen,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Quantity: $quantity',
                style: const TextStyle(
                  fontSize: 13,
                  color: AppColors.textMuted,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceFooter() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.darkEliteGreen, width: 1),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowGreen.withOpacity(0.1),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              _invoiceData!.company.footerText ?? 'Thank you for your purchase! For any inquiries, please contact our customer service.',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontStyle: FontStyle.italic,
                color: AppColors.textGreen,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'This is a computer-generated invoice and does not require a signature.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 8),
          if ((_invoiceData!.company.bin != null && _invoiceData!.company.bin!.isNotEmpty) ||
              (_invoiceData!.company.tin != null && _invoiceData!.company.tin!.isNotEmpty)) ...[
            Text(
              'This invoice is issued in compliance with the ${_invoiceData!.company.vatCountry ?? ''} VAT Act 2012${_invoiceData!.company.govtOrg != null && _invoiceData!.company.govtOrg!.isNotEmpty ? ' by ${_invoiceData!.company.govtOrg}' : ''}.',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            )],
            const SizedBox(height: 8),
            Text(
              '© ${DateTime.now().year} ${_invoiceData!.company.name}. All rights reserved.',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSummary(NumberFormat currencyFormat) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.darkEliteGreen, width: 1),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowGreen.withOpacity(0.1),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Row(
              children: [
                Container(
                  width: 3,
                  height: 16,
                  decoration: BoxDecoration(
                    color: AppColors.darkEliteGreen,
                    borderRadius: BorderRadius.circular(1.5),
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  _isDeliveryInvoice() ? 'PAYMENT SUMMARY' : 'PURCHASE SUMMARY',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.darkEliteGreen,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // For delivery invoices, show a simplified summary
            if (_isDeliveryInvoice()) ...[
              _buildSummaryRow('Delivery Fee', currencyFormat.format(_invoiceData!.invoice.amount)),

              // Add platform fee if applicable
              if (_invoiceData!.purchase.platformFee > 0 || _getOrderPlatformFeePercentage() >= 0)
                _buildSummaryRow(
                  'Platform Fee (${_getOrderPlatformFeePercentage().toStringAsFixed(1)}%)',
                  currencyFormat.format(_invoiceData!.purchase.platformFee),
                ),

              // Add service fee if applicable
              if (_invoiceData!.purchase.serviceCharge > 0 || _getOrderServiceFeePercentage() >= 0)
                _buildSummaryRow(
                  'Service Fee (${_getOrderServiceFeePercentage().toStringAsFixed(1)}%)',
                  currencyFormat.format(_invoiceData!.purchase.serviceCharge),
                ),

              // Add VAT if applicable
              if (_invoiceData!.invoice.vatAmount > 0)
                _buildSummaryRow(
                  'VAT (${_getVatPercentage().toStringAsFixed(1)}%)',
                  currencyFormat.format(_invoiceData!.invoice.vatAmount),
                ),

              // Add Card Fee if available in metadata
              if (_invoiceData!.invoice.metadata != null &&
                  _invoiceData!.invoice.metadata!.containsKey('card_fee') &&
                  double.parse(_invoiceData!.invoice.metadata!['card_fee'].toString()) > 0)
                _buildSummaryRow(
                  'Card Fee (${_getOrderCardFeePercentage().toStringAsFixed(1)}%)',
                  currencyFormat.format(double.parse(_invoiceData!.invoice.metadata!['card_fee'].toString())),
                ),

              // Add Coupon Discount if available
              if (_invoiceData!.invoice.discountAmount > 0) ...[
                _buildSummaryRow(
                  'Discount${_invoiceData!.invoice.couponCode != null ? ' (Coupon: ${_invoiceData!.invoice.couponCode})' : ''}${_invoiceData!.invoice.discountType == 'percentage' ? ' (${_invoiceData!.invoice.discountPercentage.toStringAsFixed(1)}%)' : ''}',
                  '-${currencyFormat.format(_invoiceData!.invoice.discountAmount)}',
                  isDiscount: true,
                ),

                // Show original total if available
                if (_invoiceData!.invoice.originalTotal != null)
                  _buildSummaryRow(
                    'Original Total',
                    currencyFormat.format(_invoiceData!.invoice.originalTotal!),
                    isStrikeThrough: true,
                  ),
              ],

              const Divider(height: 24),
              _buildSummaryRow(
                'Total',
                currencyFormat.format(_invoiceData!.invoice.totalAmount),
                isTotal: true,
              ),
            ]
            // For purchase invoices, show the full summary
            else ...[
              _buildSummaryRow('Subtotal', currencyFormat.format(_invoiceData!.invoice.amount)),

              // Add Platform Fee if available in tax details
              if (_invoiceData!.purchase.platformFee > 0 || _getOrderPlatformFeePercentage() >= 0)
                _buildSummaryRow(
                  'Platform Fee (${_getOrderPlatformFeePercentage().toStringAsFixed(1)}%)',
                  currencyFormat.format(_invoiceData!.purchase.platformFee),
                ),

              // Add Service Charge if available in tax details
              if (_invoiceData!.purchase.serviceCharge > 0 || _getOrderServiceFeePercentage() >= 0)
                _buildSummaryRow(
                  'Service Fee (${_getOrderServiceFeePercentage().toStringAsFixed(1)}%)',
                  currencyFormat.format(_invoiceData!.purchase.serviceCharge),
                ),

              // Add Card Fee if available in metadata
              if (_invoiceData!.invoice.metadata != null &&
                  _invoiceData!.invoice.metadata!.containsKey('card_fee') &&
                  double.parse(_invoiceData!.invoice.metadata!['card_fee'].toString()) > 0)
                _buildSummaryRow(
                  'Card Fee (${_getOrderCardFeePercentage().toStringAsFixed(1)}%)',
                  currencyFormat.format(double.parse(_invoiceData!.invoice.metadata!['card_fee'].toString())),
                ),

              // Add VAT
              if (_invoiceData!.invoice.vatAmount > 0)
                _buildSummaryRow(
                  'VAT (${_getVatPercentage().toStringAsFixed(1)}%)',
                  currencyFormat.format(_invoiceData!.invoice.vatAmount),
                ),

              // Add TDS (Tax Deducted at Source) if applicable
              if (_invoiceData!.invoice.tdsAmount > 0)
                _buildSummaryRow(
                  'TDS (${_invoiceData!.invoice.tdsPercentage.toStringAsFixed(2)}%)',
                  currencyFormat.format(_invoiceData!.invoice.tdsAmount),
                ),

              // Add Coupon Discount if available
              if (_invoiceData!.invoice.discountAmount > 0) ...[
                _buildSummaryRow(
                  'Discount${_invoiceData!.invoice.couponCode != null ? ' (Coupon: ${_invoiceData!.invoice.couponCode})' : ''}${_invoiceData!.invoice.discountType == 'percentage' ? ' (${_invoiceData!.invoice.discountPercentage.toStringAsFixed(1)}%)' : ''}',
                  '-${currencyFormat.format(_invoiceData!.invoice.discountAmount)}',
                  isDiscount: true,
                ),

                // Show original total if available
                if (_invoiceData!.invoice.originalTotal != null)
                  _buildSummaryRow(
                    'Original Total',
                    currencyFormat.format(_invoiceData!.invoice.originalTotal!),
                    isStrikeThrough: true,
                  ),
              ],

              const Divider(height: 24),
              _buildSummaryRow(
                'Total',
                currencyFormat.format(_invoiceData!.invoice.totalAmount),
                isTotal: true,
              ),

              if (_invoiceData!.invoice.goldAmount > 0 || _invoiceData!.purchase.goldAmount > 0 ||
                  _invoiceData!.invoice.silverAmount > 0 || _invoiceData!.purchase.silverAmount > 0) ...[
                const SizedBox(height: 16),
                const Divider(height: 1),
                const SizedBox(height: 16),
                const Text(
                  'Purchased Metals',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textGreen,
                  ),
                ),
                const SizedBox(height: 12),
                if (_invoiceData!.invoice.goldAmount > 0 || _invoiceData!.purchase.goldAmount > 0)
                  _buildMetalSummaryRow(
                    'Gold',
                    '${(_invoiceData!.invoice.goldAmount > 0 ? _invoiceData!.invoice.goldAmount : _invoiceData!.purchase.goldAmount).toStringAsFixed(3)} g',
                    AppColors.golden,
                  ),
                if (_invoiceData!.invoice.silverAmount > 0 || _invoiceData!.purchase.silverAmount > 0)
                  _buildMetalSummaryRow(
                    'Silver',
                    '${(_invoiceData!.invoice.silverAmount > 0 ? _invoiceData!.invoice.silverAmount : _invoiceData!.purchase.silverAmount).toStringAsFixed(3)} g',
                    AppColors.silverPrimary,
                  ),
              ],
            ],

            const SizedBox(height: 16),
            if (_invoiceData!.payment != null) ...[
              const Divider(),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Payment Method: ${_invoiceData!.payment!.method}'),
                  Text('Date: ${_invoiceData!.payment!.date}'),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('Payment Status:'),
                  Text(
                    _invoiceData!.payment!.status.toUpperCase(),
                    style: TextStyle(
                      color: _invoiceData!.payment!.status == 'completed'
                          ? Colors.green
                          : Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {
    bool isTotal = false,
    bool isDiscount = false,
    bool isStrikeThrough = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal || isDiscount ? FontWeight.bold : FontWeight.w500,
              color: isTotal
                  ? AppColors.textGreen
                  : isDiscount
                      ? Colors.green.shade700
                      : AppColors.textMuted,
              decoration: isStrikeThrough ? TextDecoration.lineThrough : null,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal || isDiscount ? FontWeight.bold : FontWeight.w500,
              color: isTotal
                  ? AppColors.textGreen
                  : isDiscount
                      ? Colors.green.shade700
                      : isStrikeThrough
                          ? AppColors.textMuted
                          : AppColors.textDark,
              decoration: isStrikeThrough ? TextDecoration.lineThrough : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetalSummaryRow(String metal, String weight, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          // Custom metal bar icon with chemical symbol
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: color, width: 1),
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.2),
                  blurRadius: 2,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Center(
              child: Container(
                width: 14,
                height: 9,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(2),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 1,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            metal,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textGreen,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          Text(
            weight,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build detail rows
  Widget _buildDetailRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: AppColors.textMuted,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: valueColor ?? AppColors.textDark,
            ),
            textAlign: TextAlign.end,
          ),
        ],
      ),
    );
  }

  // Helper methods to get fee percentages from order data
  double _getOrderPlatformFeePercentage() {
    // First check if we have an associated order with platform fee percentage
    if (_invoiceData!.associatedOrders != null && _invoiceData!.associatedOrders!.isNotEmpty) {
      final order = _invoiceData!.associatedOrders!.first;
      if (order.containsKey('platform_fee_percentage')) {
        return double.parse(order['platform_fee_percentage'].toString());
      }
    }

    // Then check if we have it in the invoice metadata
    if (_invoiceData!.invoice.metadata != null &&
        _invoiceData!.invoice.metadata!.containsKey('platform_fee_percentage')) {
      return double.parse(_invoiceData!.invoice.metadata!['platform_fee_percentage'].toString());
    }

    // Default value - use 0.0 instead of hardcoded 5.0
    return 0.0;
  }

  double _getOrderServiceFeePercentage() {
    // First check if we have an associated order with service fee percentage
    if (_invoiceData!.associatedOrders != null && _invoiceData!.associatedOrders!.isNotEmpty) {
      final order = _invoiceData!.associatedOrders!.first;
      if (order.containsKey('service_fee_percentage')) {
        return double.parse(order['service_fee_percentage'].toString());
      }
    }

    // Then check if we have it in the invoice metadata
    if (_invoiceData!.invoice.metadata != null &&
        _invoiceData!.invoice.metadata!.containsKey('service_fee_percentage')) {
      return double.parse(_invoiceData!.invoice.metadata!['service_fee_percentage'].toString());
    }

    // Default value - use 0.0 instead of hardcoded 2.0
    return 0.0;
  }

  double _getOrderCardFeePercentage() {
    // First check if we have an associated order with card fee percentage
    if (_invoiceData!.associatedOrders != null && _invoiceData!.associatedOrders!.isNotEmpty) {
      final order = _invoiceData!.associatedOrders!.first;
      if (order.containsKey('card_fee_percentage')) {
        return double.parse(order['card_fee_percentage'].toString());
      }
    }

    // Then check if we have it in the invoice metadata
    if (_invoiceData!.invoice.metadata != null &&
        _invoiceData!.invoice.metadata!.containsKey('card_fee_percentage')) {
      return double.parse(_invoiceData!.invoice.metadata!['card_fee_percentage'].toString());
    }

    // Default value - use 0.0 instead of hardcoded 2.3
    return 0.0;
  }

  double _getVatPercentage() {
    // First check if we have an associated order with VAT percentage
    if (_invoiceData!.associatedOrders != null && _invoiceData!.associatedOrders!.isNotEmpty) {
      final order = _invoiceData!.associatedOrders!.first;
      if (order.containsKey('vat_percentage')) {
        return double.parse(order['vat_percentage'].toString());
      }
    }

    // Then check if we have it directly in the invoice
    if (_invoiceData!.invoice.vatPercentage > 0) {
      return _invoiceData!.invoice.vatPercentage;
    }

    // Then check if we have it in the invoice metadata
    if (_invoiceData!.invoice.metadata != null &&
        _invoiceData!.invoice.metadata!.containsKey('vat_percentage')) {
      return double.parse(_invoiceData!.invoice.metadata!['vat_percentage'].toString());
    }

    // Default value based on the backend default - use 5.0 as this is the correct default for VAT
    return 5.0;
  }

  // Method to handle invoice printing/downloading
  void _printInvoice() async {
    // For mobile, we'll use the JSON data to generate a PDF
    if (_invoiceData != null) {
      try {
        // Show loading indicator with more detailed message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Preparing invoice for download with updated format...'),
            duration: Duration(seconds: 2),
          ),
        );

        // Use the downloadInvoice method from the invoice service
        // This will use the updated calculations and display format from the backend
        // The updated parameter has been added to ensure the backend uses the new format
        final result = await _serviceProvider.invoiceService.downloadInvoice(_invoiceData!.invoice.id);

        if (result['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'Invoice download initiated'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'Failed to download invoice'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 4),
              action: SnackBarAction(
                label: 'Retry',
                onPressed: _printInvoice,
                textColor: Colors.white,
              ),
            ),
          );
        }
      } catch (e) {
        print('Error downloading invoice: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error downloading invoice: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _printInvoice,
              textColor: Colors.white,
            ),
          ),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Invoice data not available for download'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 4),
        ),
      );
    }
  }
}
