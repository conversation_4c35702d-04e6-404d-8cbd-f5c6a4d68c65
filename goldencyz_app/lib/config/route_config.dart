/// Configuration for route separation between web and API
///
/// This class helps maintain separation between web and API routes
/// to ensure API endpoints remain unimpacted when changing web version.
class RouteConfig {
  // Base URLs
  // Production URLs with HTTPS
  static const String apiBaseUrl = 'https://api.goldencyz.com/api'; // For API endpoints
  static const String webBaseUrl = 'https://goldencyz.com';         // For web endpoints

  // For development, uncomment these lines instead:
  // static const String apiBaseUrl = 'http://*************:8001/api'; // For API endpoints
  // static const String webBaseUrl = 'http://*************:8001';     // For web endpoints

  // Local development (only works when running on the same machine)
  // static const String apiBaseUrl = 'http://127.0.0.1:8001/api'; // For API endpoints
  // static const String webBaseUrl = 'http://127.0.0.1:8001';     // For web endpoints

  // Extract host and port for diagnostics
  static const String apiHost = 'api.goldencyz.com';
  static const int apiPort = 443; // HTTPS port

  // API route builder
  static String apiRoute(String path) {
    return '$apiBaseUrl$path';
  }

  // Web route builder
  static String webRoute(String path) {
    return '$webBaseUrl$path';
  }

  // Determine if a route is an API route
  static bool isApiRoute(String route) {
    return route.startsWith('/api') || route.contains(apiBaseUrl);
  }

  // Determine if a route is a web route
  static bool isWebRoute(String route) {
    return !isApiRoute(route);
  }
}
