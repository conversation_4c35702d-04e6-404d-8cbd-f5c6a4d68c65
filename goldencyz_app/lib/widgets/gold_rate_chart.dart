import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:goldencyz_app/constants.dart';
import 'package:goldencyz_app/models/gold_rate_history.dart';
import 'package:goldencyz_app/services/service_provider.dart';
import 'package:intl/intl.dart';

class GoldRateChart extends StatefulWidget {
  final int days;
  final double height;
  final bool showTitle;
  final bool showLabels;
  final bool showGrid;
  final bool showBorder;
  final bool showGradient;
  final bool showDots;

  const GoldRateChart({
    super.key,
    this.days = 14, // Default to 7 days for dashboard
    this.height = 200,
    this.showTitle = true,
    this.showLabels = true,
    this.showGrid = true,
    this.showBorder = true,
    this.showGradient = true,
    this.showDots = false,
  });

  @override
  State<GoldRateChart> createState() => _GoldRateChartState();
}

class _GoldRateChartState extends State<GoldRateChart> {
  final ServiceProvider _serviceProvider = ServiceProvider();
  bool _isLoading = true;
  List<GoldRateHistory> _rateHistory = [];
  String _dataSource = 'Loading...';

  @override
  void initState() {
    super.initState();
    _fetchRateHistory();
  }

  Future<void> _fetchRateHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize the service if needed
      if (_serviceProvider.useRealGoldRates) {
        await _serviceProvider.goldRateService.init();
      }

      List<GoldRateHistory> history;

      // First try to get data from cache if available
      if (_serviceProvider.useRealGoldRates) {
        try {
          // Try to get data from cache first
          final cachedData = await _serviceProvider.goldRateService.cacheService.getCachedRates(widget.days);
          if (cachedData != null && cachedData.isNotEmpty) {
            if (mounted) {
              setState(() {
                _rateHistory = cachedData;
                _isLoading = false;
                _dataSource = 'Cached Data';
              });
            }
            // Continue fetching fresh data in the background
          }
        } catch (e) {
          print('Error getting cached data: $e');
          // Continue with API call
        }

        try {
          // Use the enhanced service with caching
          history = await _serviceProvider.goldRateService.getRateHistory(
            days: widget.days,
            forceRefresh: false, // Don't force refresh to allow using cache
          );

          if (mounted) {
            setState(() {
              _rateHistory = history;
              _isLoading = false;
              _dataSource = 'API';
            });
          }
        } catch (e) {
          print('Error fetching from API: $e');
          // If API fails but we already have cached data, don't show error
          if (_rateHistory.isEmpty) {
            // Use demo service as fallback
            history = await _serviceProvider.demoGoldRateHistoryService.getHistory(days: widget.days);
            if (mounted) {
              setState(() {
                _rateHistory = history;
                _isLoading = false;
                _dataSource = 'Demo (API Error)';
              });
            }
          }
        }
      } else {
        // Use demo service directly
        history = await _serviceProvider.demoGoldRateHistoryService.getHistory(days: widget.days);
        if (mounted) {
          setState(() {
            _rateHistory = history;
            _isLoading = false;
            _dataSource = 'Demo';
          });
        }
      }
    } catch (e) {
      print('Error in _fetchRateHistory: $e');

      // Try to use demo data as fallback
      try {
        final history = await _serviceProvider.demoGoldRateHistoryService.getHistory(days: widget.days);
        if (mounted) {
          setState(() {
            _rateHistory = history;
            _isLoading = false;
            _dataSource = 'Demo (Fallback)';
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _dataSource = 'Error';
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.white, // White background
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.darkEliteGreen), // Green border
      ),
      child: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.textGreen), // Green color
              ),
            )
          : _rateHistory.isEmpty
              ? const Center(
                  child: Text(
                    'No data available',
                    style: TextStyle(
                      color: AppColors.textGreen, // Green text
                    ),
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (widget.showTitle) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Gold Price Trend (7 Days)',
                              style: TextStyle(
                                color: AppColors.golden,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            if (_rateHistory.isNotEmpty)
                              Text(
                                'Latest: ৳${NumberFormat('#,###').format(_rateHistory.last.k22)}',
                                style: const TextStyle(
                                  color: AppColors.golden,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 16),
                      ],
                      Expanded(
                        child: _rateHistory.length < 2
                            ? const Center(
                                child: Text(
                                  'Not enough data to display chart',
                                  style: TextStyle(
                                    color: AppColors.textLight,
                                  ),
                                ),
                              )
                            : LineChart(
                                LineChartData(
                                  gridData: FlGridData(
                                    show: true, // Always show grid for better visibility
                                    drawVerticalLine: true,
                                    horizontalInterval: 1000,
                                    verticalInterval: 1,
                                    getDrawingHorizontalLine: (value) {
                                      return FlLine(
                                        color: AppColors.golden.withOpacity(0.3), // Increased opacity
                                        strokeWidth: 1.5, // Increased width
                                        dashArray: [5, 5],
                                      );
                                    },
                                    getDrawingVerticalLine: (value) {
                                      return FlLine(
                                        color: Colors.grey.withOpacity(0.5), // Increased opacity
                                        strokeWidth: 1.5, // Increased width
                                        dashArray: [5, 5],
                                      );
                                    },
                                  ),
                                  // Add tooltip styling with white text
                                  lineTouchData: LineTouchData(
                                    touchTooltipData: LineTouchTooltipData(
                                      getTooltipColor: (touchedSpot) => AppColors.textGreen, // Green background
                                      tooltipRoundedRadius: 8,
                                      getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                                        return touchedBarSpots.map((barSpot) {
                                          final flSpot = barSpot;
                                          if (flSpot.x >= 0 && flSpot.x.toInt() < _rateHistory.length) {
                                            final date = DateFormat('MMM dd, yyyy').format(
                                              DateTime.parse(_rateHistory[flSpot.x.toInt()].date),
                                            );
                                            return LineTooltipItem(
                                              '$date\n',
                                              const TextStyle(
                                                color: Colors.white, // White text
                                                fontWeight: FontWeight.bold,
                                                fontSize: 12,
                                              ),
                                              children: [
                                                TextSpan(
                                                  text: '৳${NumberFormat('#,###').format(flSpot.y)}',
                                                  style: const TextStyle(
                                                    color: Colors.white, // White text
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ],
                                            );
                                          }
                                          return null;
                                        }).toList();
                                      },
                                    ),
                                  ),
                                  titlesData: FlTitlesData(
                                    show: widget.showLabels,
                                    rightTitles: const AxisTitles(
                                      sideTitles: SideTitles(showTitles: false),
                                    ),
                                    topTitles: const AxisTitles(
                                      sideTitles: SideTitles(showTitles: false),
                                    ),
                                    bottomTitles: AxisTitles(
                                      sideTitles: SideTitles(
                                        showTitles: widget.showLabels,
                                        reservedSize: 30,
                                        interval: _rateHistory.length > 10
                                            ? (_rateHistory.length / 5).floor().toDouble()
                                            : 1,
                                        getTitlesWidget: (value, meta) {
                                          if (value.toInt() >= 0 &&
                                              value.toInt() < _rateHistory.length) {
                                            return SideTitleWidget(
                                              space: 4,
                                              angle: 45,
                                              meta: meta,
                                              child: Text(
                                                DateFormat('dd MMM')
                                                    .format(DateTime.parse(_rateHistory[value.toInt()].date)),
                                                style: TextStyle(
                                                  color: AppColors.textDark,
                                                  fontSize: 11,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            );
                                          }
                                          return const SizedBox();
                                        },
                                      ),
                                    ),
                                    leftTitles: AxisTitles(
                                      sideTitles: SideTitles(
                                        showTitles: widget.showLabels,
                                        interval: 2000,
                                        getTitlesWidget: (value, meta) {
                                          return SideTitleWidget(
                                            space: 4,
                                            angle: 0,
                                            meta: meta,
                                            child: Text(
                                              value.toInt().toString(),
                                              style: TextStyle(
                                                color: AppColors.textDark,
                                                fontSize: 11,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          );
                                        },
                                        reservedSize: 40,
                                      ),
                                    ),
                                  ),
                                  borderData: FlBorderData(
                                    show: true, // Always show border for better visibility
                                    border: Border.all(
                                      color: AppColors.golden.withOpacity(0.5), // Increased opacity
                                      width: 1.5, // Increased width
                                    ),
                                  ),
                                  minX: 0,
                                  maxX: (_rateHistory.length - 1).toDouble(),
                                  minY: _rateHistory
                                          .map((e) => e.k22)
                                          .reduce((a, b) => a < b ? a : b) *
                                      0.95,
                                  maxY: _rateHistory
                                          .map((e) => e.k22)
                                          .reduce((a, b) => a > b ? a : b) *
                                      1.05,
                                  lineBarsData: [
                                    LineChartBarData(
                                      spots: List.generate(
                                        _rateHistory.length,
                                        (index) => FlSpot(
                                          index.toDouble(),
                                          _rateHistory[index].k22,
                                        ),
                                      ),
                                      isCurved: true,
                                      color: AppColors.textGreen, // Green line
                                      barWidth: 4.0, // Increased width
                                      isStrokeCapRound: true,
                                      dotData: FlDotData(
                                        show: true, // Always show dots for better visibility
                                        getDotPainter: (spot, percent, barData, index) {
                                          return FlDotCirclePainter(
                                            radius: 5,
                                            color: Colors.white,
                                            strokeWidth: 2.5,
                                            strokeColor: AppColors.textGreen,
                                          );
                                        },
                                      ),
                                      belowBarData: widget.showGradient
                                          ? BarAreaData(
                                              show: true,
                                              color: AppColors.textGreen.withOpacity(0.3),
                                              gradient: LinearGradient(
                                                colors: [
                                                  AppColors.textGreen.withOpacity(0.4),
                                                  AppColors.textGreen.withOpacity(0.1),
                                                ],
                                                begin: Alignment.topCenter,
                                                end: Alignment.bottomCenter,
                                              ),
                                            )
                                          : null,
                                    ),
                                  ],
                                ),
                              ),
                      ),
                      if (widget.showLabels) ...[
                        const SizedBox(height: 8),
                        Center(
                          child: Text(
                            'Data source: $_dataSource',
                            style: TextStyle(
                              fontSize: 10,
                              color: AppColors.textLight.withOpacity(0.7),
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
    );
  }
}
