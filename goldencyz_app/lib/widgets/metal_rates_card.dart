import 'package:flutter/material.dart';
import 'package:goldencyz_app/constants.dart';
import 'package:goldencyz_app/models/gold_rate.dart';
import 'package:goldencyz_app/models/silver_rate.dart';
import 'package:intl/intl.dart';

class MetalRatesCard extends StatelessWidget {
  final GoldRate? goldRate;
  final SilverRate? silverRate;
  final bool isLoading;
  const MetalRatesCard({
    super.key,
    required this.goldRate,
    required this.silverRate,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(
      locale: 'en_BD',
      symbol: '৳',
      decimalDigits: 2,
    );

    final dateFormat = DateFormat('dd MMM yyyy');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white, // White background
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.darkEliteGreen), // Green border
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowGreen.withOpacity(0.2),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  // Green vertical bar
                  Container(
                    width: 3,
                    height: 18,
                    decoration: BoxDecoration(
                      color: AppColors.darkEliteGreen,
                      borderRadius: BorderRadius.circular(1.5),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Title
                  const Text(
                    'CURRENT RATES',
                    style: TextStyle(
                      color: AppColors.textGreen, // Green text
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),

            ],
          ),
          const SizedBox(height: 16),
          isLoading
              ? const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.textGreen), // Green color
                  ),
                )
              : Row(
                  children: [
                    // Gold rate
                    Expanded(
                      child: _buildRateCard(
                        context,
                        title: 'Gold (22K)',
                        rate: goldRate?.pricePerGram ?? 0,
                        color: AppColors.golden,
                        icon: Icons.monetization_on,
                        source: goldRate?.source ?? 'N/A',
                        karat: goldRate?.karat ?? '22K',
                        date: goldRate?.date ?? DateTime.now(),
                        dateFormat: dateFormat,
                        currencyFormat: currencyFormat,
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Silver rate
                    Expanded(
                      child: _buildRateCard(
                        context,
                        title: 'Silver (23.88K)',
                        rate: silverRate != null ? double.parse(silverRate!.pricePerGram.toString()) : 0,
                        color: AppColors.silverPrimary,
                        icon: Icons.monetization_on_outlined,
                        source: silverRate?.source ?? 'N/A',
                        karat: silverRate?.karat ?? 'Fine',
                        date: silverRate != null ? DateTime.parse(silverRate!.date) : DateTime.now(),
                        dateFormat: dateFormat,
                        currencyFormat: currencyFormat,
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  Widget _buildRateCard(
    BuildContext context, {
    required String title,
    required double rate,
    required Color color,
    required IconData icon,
    required String source,
    String? karat,
    required DateTime date,
    required DateFormat dateFormat,
    required NumberFormat currencyFormat,
  }) {
    // Format the rate with Taka symbol
    final formattedRate = rate.toStringAsFixed(2);

    // Determine purity text based on karat value
    String purityText = '';
    print('DEBUG: Metal type: $title, Karat value: $karat');
    if (title.contains('Gold')) {
      // Use the karat value from the API
      if (karat == '22K') {
        purityText = '22 Karat • 91.6% Pure';
      } else if (karat == '24K') {
        purityText = '24 Karat • 99.9% Pure';
      } else if (karat == '23.5K') {
        purityText = '23.5 Karat • 97.9% Pure';
      } else if (karat == '21K') {
        purityText = '21 Karat • 87.5% Pure';
      } else if (karat == '18K') {
        purityText = '18 Karat • 75.0% Pure';
      } else {
        // Default to the passed karat value
        purityText = '${karat ?? "22K"} • 91.6% Pure';
      }
    } else if (title.contains('Silver')) {
      // Use the karat value from the API
      if (karat == 'Fine') {
        purityText = 'Fine • 99.5% Pure';
      } else if (karat == 'Sterling') {
        purityText = 'Sterling • 92.5% Pure';
      } else if (karat == 'Britannia') {
        purityText = 'Britannia • 95.8% Pure';
      } else {
        // Default to Fine
        purityText = '${karat ?? "Fine"} • 99.5% Pure';
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white, // White background
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.darkEliteGreen, width: 1), // Green border
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowGreen.withOpacity(0.2),
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title row with metal type
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
              fontSize: 14,
            ),
          ),

          // Purity information
          Text(
            purityText,
            style: TextStyle(
              color: AppColors.textMedium,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),

          const SizedBox(height: 8),

          // Rate display with Taka symbol
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              // Taka symbol
              Text(
                '৳',
                style: TextStyle(
                  color: AppColors.textGreen,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(width: 2),
              // Rate value
              Text(
                formattedRate,
                style: const TextStyle(
                  color: AppColors.textGreen, // Green text
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              const SizedBox(width: 4),
              // Per gram text
              Text(
                '/g',
                style: TextStyle(
                  color: AppColors.textMedium,
                  fontSize: 12,
                ),
              ),
            ],
          ),

          // Date information
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Updated: ${dateFormat.format(date)}',
                  style: TextStyle(
                    color: AppColors.textMedium.withOpacity(0.7),
                    fontSize: 10,
                  ),
                ),
                Text(
                  'Source: $source',
                  style: TextStyle(
                    color: AppColors.textMedium.withOpacity(0.7),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
